<script setup lang="ts">
import { defineProps, onMounted, onBeforeUnmount, ref, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

interface ChartDataPoint {
  x: number;
  y: number;
}

const props = defineProps({
  predictedData: { type: Array as () => ChartDataPoint[], required: true },
  originalData: { type: Array as () => ChartDataPoint[], required: true },
  r2: { type: Number, required: true },
  ridgeR2: { type: Number, required: true },
  diff: { type: Number, required: true },
  title: { type: String, required: true }
});

const chartContainer = ref<HTMLDivElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

function getMaxPoint() {
  const maxY = Math.max(...props.predictedData.map(p => p.y));
  return props.predictedData.find(p => p.y === maxY) || { x: 0, y: 0 };
}

function createChart() {
  if (!chartContainer.value) return;

  // Dispose existing chart instance
  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value, null, {
    renderer: 'canvas',
    useDirtyRect: false
  });

  const maxPoint = getMaxPoint();

  const option = {
    title: {
      text: props.title,
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#374151'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#16A34A'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#16A34A',
      borderWidth: 1,
      textStyle: {
        color: '#374151'
      },
      formatter: function(params: any) {
        let result = '';
        params.forEach((param: any) => {
          if (param.seriesName === 'Predicted Productivity') {
            result += `${param.marker}${param.seriesName}: ${param.value[1].toFixed(2)}<br/>`;
            result += `LM: ${param.value[0].toFixed(1)}<br/>`;
          } else {
            result += `${param.marker}${param.seriesName}: ${param.value[1].toFixed(2)}<br/>`;
            result += `LM: ${param.value[0].toFixed(1)}<br/>`;
          }
        });
        return result;
      }
    },
    legend: {
      data: ['Predicted Productivity', 'Original Data'],
      top: 35,
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#374151'
      }
    },
    grid: {
      left: '60px',
      right: '40px',
      top: '80px',
      bottom: '60px',
      containLabel: false
    },
    xAxis: {
      type: 'value',
      name: 'Linear Meter (LM)',
      nameLocation: 'middle',
      nameGap: 35,
      nameTextStyle: {
        fontSize: 12,
        color: '#374151'
      },
      axisLine: {
        lineStyle: {
          color: '#9CA3AF'
        }
      },
      axisLabel: {
        formatter: '{value}',
        color: '#6B7280'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#F3F4F6',
          type: 'solid'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: 'Productivity',
      nameLocation: 'middle',
      nameGap: 45,
      nameTextStyle: {
        fontSize: 12,
        color: '#374151'
      },
      axisLine: {
        lineStyle: {
          color: '#9CA3AF'
        }
      },
      axisLabel: {
        formatter: '{value}',
        color: '#6B7280'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#F3F4F6',
          type: 'solid'
        }
      }
    },
    series: [
      {
        name: 'Predicted Productivity',
        type: 'line',
        data: props.predictedData.map(p => [p.x, p.y]),
        smooth: true,
        smoothMonotone: 'x',
        lineStyle: {
          color: '#16A34A',
          width: 3
        },
        itemStyle: {
          color: '#16A34A'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(22, 163, 74, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(22, 163, 74, 0.05)'
              }
            ]
          }
        },
        symbol: 'none',
        emphasis: {
          focus: 'series'
        }
      },
      {
        name: 'Original Data',
        type: 'scatter',
        data: props.originalData.map(p => [p.x, p.y]),
        symbolSize: 8,
        itemStyle: {
          color: '#059669',
          borderColor: '#047857',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            color: '#047857',
            borderColor: '#065f46',
            shadowBlur: 10,
            shadowColor: 'rgba(22, 163, 74, 0.5)'
          }
        }
      }
    ],
    graphic: [
      {
        type: 'line',
        shape: {
          x1: 0, // Will be updated dynamically
          y1: 0,
          x2: 0,
          y2: 0
        },
        style: {
          stroke: '#22C55E',
          lineWidth: 2,
          lineDash: [8, 4]
        },
        silent: true,
        z: 100
      },
      
    ]
  };

  chartInstance.setOption(option);

  // Update saturation line position after chart is rendered
  nextTick(() => {
    if (chartInstance) {
      const pixelPoint = chartInstance.convertToPixel('grid', [maxPoint.x, 0]);
      const gridRect = chartInstance.getModel().getComponent('grid').coordinateSystem.getRect();
      
      chartInstance.setOption({
        graphic: [
          {
            id: 'saturationLine',
            type: 'line',
            shape: {
              x1: pixelPoint[0],
              y1: gridRect.y,
              x2: pixelPoint[0],
              y2: gridRect.y + gridRect.height
            },
            style: {
              stroke: '#22C55E',
              lineWidth: 2,
              lineDash: [8, 4]
            }
          },
          
        ]
      });
    }
  });

  // Handle window resize for responsiveness
  const handleResize = () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  };

  window.addEventListener('resize', handleResize);
  
  // Store resize handler for cleanup
  (chartInstance as any)._resizeHandler = handleResize;
}

onMounted(() => {
  nextTick(() => {
    createChart();
  });
});

watch(props, () => {
  createChart();
}, { deep: true });

onBeforeUnmount(() => {
  if (chartInstance) {
    // Remove resize handler
    if ((chartInstance as any)._resizeHandler) {
      window.removeEventListener('resize', (chartInstance as any)._resizeHandler);
    }
    chartInstance.dispose();
  }
});
</script>

<template>
  <div 
    ref="chartContainer" 
    class="chart-container"
    style="width: 100%; height: 350px; min-height: 300px;"
  ></div>
</template>

<style scoped>
.chart-container {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px !important;
    min-height: 250px !important;
  }
}

@media (max-width: 480px) {
  .chart-container {
    height: 280px !important;
    min-height: 220px !important;
  }
}
</style>