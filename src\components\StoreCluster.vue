<template>
  <div class="w-full flex flex-col p-4 overflow-x-auto max-w-full">
    <table class="w-full table-auto border-collapse border border-gray-300">
      <thead class="bg-primary text-xs uppercase tracking-wide sticky top-0 z-10">
        <tr>
          <th class="px-4 py-2 w-24 border border-gray-300">Cluster</th>
          <th class="w-8 border border-gray-300"></th>
          <th class="px-4 py-2 w-24 border border-gray-300">Store ID</th>
          <th class="px-4 py-2 w-24 border border-gray-300">Store Name</th>
          <th class="px-4 py-2 min-w-[140px] border border-gray-300">REVENUE_PER_SQFT</th>
          <th class="px-4 py-2 min-w-[80px] border border-gray-300">Area_SQFT</th>
          <th class="px-4 py-2 min-w-[80px] border border-gray-300">Nationals</th>
          <th class="px-4 py-2 min-w-[100px] border border-gray-300">Arab_Expats</th>
          <th class="px-4 py-2 min-w-[100px] border border-gray-300">ISC</th>
          
          <th class="px-4 py-2 min-w-[120px] border border-gray-300">Seac</th>
          <th class="px-4 py-2 min-w-[120px] border border-gray-300">Western</th>
          <!-- <th class="px-4 py-2 min-w-[120px] border border-gray-300">Unspecified</th> -->
          
          <th class="w-16 border border-gray-300"></th>
        </tr>
      </thead>

      <tbody>
        <template v-for="(stores, cluster) in storesByCluster" :key="cluster">
          <!-- Collapsed totals row -->
          <tr
            v-if="!expandedClusters.has(cluster)"
            class="font-semibold bg-gray-100 text-xs cursor-pointer select-none hover:bg-gray-200"
            @click="toggleCluster(cluster)"
          >
            <td class="px-4 py-2 border border-gray-300 text-center">Total ({{ stores.length }})</td>
            <td class="px-2 border border-gray-300"></td>
            <td class="px-4 py-2 border border-gray-300"></td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.REVENUE_PER_SQFT?.toFixed(2) || 0 }}
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.AREA_SQFT || 0 }}
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.NATIONALS?.toFixed(2) || 0 }}%
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.EXPAT_ARAB?.toFixed(2) || 0 }}%
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.ISC?.toFixed(2) || 0 }}%
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.SEAC?.toFixed(2) || 0 }}%
            </td>
            <td class="px-4 py-2 border border-gray-300 text-right font-mono">
              {{ clusterTotals[cluster]?.WESTERN?.toFixed(2) || 0 }}%
            </td>
            <td class="px-3 border border-gray-300"></td>
          </tr>

          <!-- Expanded cluster rows -->
          <template v-else>
            <tr
              v-for="store in stores"
              :key="store.LOC_CD"
              draggable="true"
              @dragstart="onDragStart($event, store)"
              @dragover.prevent
              @drop="onDrop($event, cluster)"
              class="text-sm hover:bg-blue-50"
            >
              <td class="px-4 py-2 border border-gray-300 text-center">{{ store.CLUSTER_NUM }}</td>
              <td
                class="px-2 border border-gray-300 cursor-move text-center"
                title="Drag handle"
              >
                <GripVertical class="w-4 h-4 text-gray-400" />
              </td>
              <td class="px-4 py-2 border border-gray-300 text-center">{{ store.LOC_CD }}</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.LOC_NM }}</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.REVENUE_PER_SQFT }}</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.AREA_SQFT }}</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.NATIONALS }}%</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.EXPAT_ARAB }}%</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.ISC }}%</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.SEAC }}%</td>
              <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store.WESTERN }}%</td>
              <!-- <td class="px-4 py-2 border border-gray-300 text-right font-mono">{{ store. }}</td> -->
              <td class="px-3 border border-gray-300 text-center">
                <button
                  @click="confirmDelete(store)"
                  aria-label="Delete store"
                  class="text-red-600 hover:text-red-900 focus:outline-none"
                >
                  ✖
                </button>
              </td>
            </tr>

            <!-- Cluster totals row -->
            <tr
              class="font-semibold bg-gray-200 text-xs border-t border-gray-300 select-none"
              style="user-select: none;" @click="toggleCluster(cluster)"
            >
              <td colspan="3" class="px-4 py-2 text-left">Cluster {{ cluster }} Totals:</td>
              <td class="px-4 py-2 text-right font-mono"></td>
              <td class="px-4 py-2 text-right font-mono">{{ clusterTotals[cluster]?.REVENUE_PER_SQFT.toFixed(2) || '########' }}</td>
              <td class="px-4 py-2 text-right font-mono">{{ clusterTotals[cluster]?.AREA_SQFT.toFixed(2) || 0 }}</td>
              <td class="px-4 py-2 text-right font-mono">{{ clusterTotals[cluster]?.NATIONALS || 0 }}%</td>
              <td class="px-4 py-2 text-right font-mono">{{ clusterTotals[cluster]?.EXPAT_ARAB || 0 }}%</td>
              <td class="px-4 py-2 text-right font-mono">{{ clusterTotals[cluster]?.ISC || 0 }}%</td>
              <td class="px-4 py-2 text-right font-mono">{{ (clusterTotals[cluster]?.SEAC || 0).toFixed(2) }}%</td>
              <td class="px-4 py-2 text-right font-mono">{{ (clusterTotals[cluster]?.WESTERN || 0).toFixed(2) }}%</td>
              <td></td>
            </tr>
          </template>
        </template>
      </tbody>
    </table>

    <!-- Confirmation Modal -->
    <div v-if="modal.visible" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
      @keydown.escape.window="closeModal" tabindex="-1">
      <div class="bg-white p-6 rounded shadow-lg max-w-md w-full" role="dialog" aria-modal="true"
        aria-labelledby="modal-title">
        <p id="modal-title" class="mb-4 text-gray-900">
          Are you sure you want to move store <strong>{{ modal.store.LOC_CD }}</strong> from cluster <strong>{{
            modal.store.CLUSTER_NUM }}</strong> to cluster <strong>{{ targetCluster }}</strong>?
        </p>
        <div class="flex justify-end space-x-4">
          <button @click="performMove"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            OK
          </button>
          <button @click="closeModal"
            class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400">
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="deleteModal.visible"
      class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
      @keydown.escape.window="closeDeleteModal" tabindex="-1">
      <div class="bg-white p-6 rounded shadow-lg max-w-md w-full" role="dialog" aria-modal="true"
        aria-labelledby="delete-modal-title">
        <p id="delete-modal-title" class="mb-4 text-gray-900">
          Are you sure you want to delete store <strong>{{ deleteModal.store.LOC_CD }}</strong> from cluster <strong>{{
            deleteModal.store.CLUSTER_NUM }}</strong>?
        </p>
        <div class="flex justify-end space-x-4">
          <button @click="performDelete"
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
            Delete
          </button>
          <button @click="closeDeleteModal"
            class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRaw, onMounted, reactive, ref, computed } from "vue";
import { GripVertical } from 'lucide-vue-next';
import { deleteStoreFromClusterAPI, updateStoreClustersAPI, getClustersAPI } from '../services/api'

const stores = ref([]);

// Group stores by cluster
const storesByCluster = computed(() => {
  const grouped = {};
  for (const store of stores.value) {
    if (!grouped[store.CLUSTER_NUM]) grouped[store.CLUSTER_NUM] = [];
    grouped[store.CLUSTER_NUM].push(store);
  }
  return grouped;
});

// Compute totals per cluster
const clusterTotals = computed(() => {
  const totals = {};
  for (const [cluster, clusterStores] of Object.entries(storesByCluster.value)) {
    totals[cluster] = {
      AREA_SQFT: 0,
      REVENUE_PER_SQFT: 0,
      NATIONALS: 0,
      EXPAT_ARAB: 0,
      ISC: 0,
      SEAC: 0,
      WESTERN: 0,
      count: 0
    };

    for (const s of clusterStores) {
      totals[cluster].AREA_SQFT += Number(s.AREA_SQFT) || 0;
      totals[cluster].REVENUE_PER_SQFT += Number(s.REVENUE_PER_SQFT) || 0;

      totals[cluster].NATIONALS += Number(s.NATIONALS) || 0;
      totals[cluster].EXPAT_ARAB += Number(s.EXPAT_ARAB) || 0;
      totals[cluster].ISC += Number(s.ISC) || 0;
      totals[cluster].SEAC += Number(s.SEAC) || 0;
      totals[cluster].WESTERN += Number(s.WESTERN) || 0;

      totals[cluster].count++;
    }

    if (totals[cluster].count > 0) {
      totals[cluster].REVENUE_PER_SQFT /= totals[cluster].count;
      totals[cluster].NATIONALS /= totals[cluster].count;
      totals[cluster].EXPAT_ARAB /= totals[cluster].count;
      totals[cluster].ISC /= totals[cluster].count;
      totals[cluster].SEAC /= totals[cluster].count;
      totals[cluster].WESTERN /= totals[cluster].count;
    }
  }

  return totals;
});


// Expanded clusters tracking
const expandedClusters = ref(new Set());

function toggleCluster(cluster) {
  if (expandedClusters.value.has(cluster)) {
    expandedClusters.value.delete(cluster);
  } else {
    expandedClusters.value.add(cluster);
  }
  
}

// Drag-and-drop and modal logic
const draggedStore = ref(null);
const dragSourceCluster = ref(null);
const targetCluster = ref(null);
const pendingClusterUpdates = ref([]);

function onDragStart(event, store) {
  draggedStore.value = store;
  dragSourceCluster.value = store.CLUSTER_NUM;
  event.dataTransfer.effectAllowed = "move";
  console.log("store values  ", store)
}

function onDrop(event, newCluster) {
  if (!draggedStore.value) return;
  if (dragSourceCluster.value === newCluster) {
    draggedStore.value = null;
    dragSourceCluster.value = null;
    return;
  }
  targetCluster.value = newCluster;
  modal.store = draggedStore.value;
  modal.visible = true;
}

const modal = reactive({ visible: false, store: null });
function closeModal() {
  modal.visible = false;
  draggedStore.value = null;
  dragSourceCluster.value = null;
  targetCluster.value = null;
}
// When the user confirms moving a store
function performMove() {
  if (!modal.store || targetCluster.value == null) return;

  const index = stores.value.findIndex(s => s.LOC_CD === modal.store.LOC_CD);
  if (index === -1) return;

  // Update local store cluster immediately (optimistic UI)
  stores.value[index].CLUSTER_NUM = targetCluster.value;

  // Add to pending changes array
  const existingUpdateIndex = pendingClusterUpdates.value.findIndex(
    u => u.loc_cd === modal.store.LOC_CD
  );
  const cncpt_name = localStorage.getItem('concept')
  if (existingUpdateIndex !== -1) {
    // Update existing entry if any
    pendingClusterUpdates.value[existingUpdateIndex].cluster_num = targetCluster.value;
  } else {
    pendingClusterUpdates.value.push({
      loc_cd: modal.store.LOC_CD,
      cluster_num: targetCluster.value,
      concept: cncpt_name
    });
  }

  closeModal();
  saveClusterUpdates()
}

// A new function to send all accumulated updates to backend

async function saveClusterUpdates() {
  if (pendingClusterUpdates.value.length === 0) {
    return;
  }

  try {
    const payload = {
      updates: toRaw(pendingClusterUpdates.value), // remove proxy here
    };

    await updateStoreClustersAPI(payload);

    // Clear pending updates after successful save
    pendingClusterUpdates.value = [];
    // alert("Cluster updates saved successfully!");
  } catch (error) {
    // alert("Failed to save cluster updates: " + error.message);
    // Optionally revert UI changes or refetch data
  }
}


const deleteModal = reactive({ visible: false, store: null });
function confirmDelete(store) {
  deleteModal.store = store;
  deleteModal.visible = true;
}
function closeDeleteModal() {
  deleteModal.visible = false;
  deleteModal.store = null;
}
async function performDelete() {
  if (!deleteModal.store) return;

  const { LOC_CD, CLUSTER_NUM } = deleteModal.store;
  const cncpt_name = localStorage.getItem('concept')
  try {
    await deleteStoreFromClusterAPI({
      loc_cd: LOC_CD,
      cluster_num: CLUSTER_NUM,
      concept: cncpt_name
    });

    const index = stores.value.findIndex(s => s.LOC_CD === LOC_CD);
    if (index !== -1) {
      stores.value.splice(index, 1);
    }

    // alert("Store deleted successfully.");
  } catch (error) {
    alert("Failed to delete store: " + (error?.detail || error?.error || "Unknown error"));
  }

  closeDeleteModal();
}

onMounted(async () => {
  const cncpt_name = localStorage.getItem('concept')
  const territory = sessionStorage.getItem('territory_name')
  const locCodes = JSON.parse(sessionStorage.getItem('loc_codes') || '[]')
  try {
    const clustersData = await getClustersAPI({
      concept: cncpt_name,
      territory: territory,
      loc_codes: locCodes
    });

    const rawStores = Object.values(clustersData).flat();
    const processed = rawStores.map(store => {
      const ethnicity = store.ethnicity_contribution || {};

      return {
        CLUSTER_NUM: store.cluster_num ?? 'Unknown',
        LOC_CD: store.loc_cd ?? 'N/A',
        LOC_NM: store.loc_nm ?? '',
        REVENUE: Number(store.revenue) || 0,
        UNITS: String(store.units ?? "0"),
        TOTAL_CUSTOMER: Number(store.total_customer) || 0,
        TOTAL_INVOICE: Number(store.total_invoice) || 0,
        AREA_SQFT: Number(store.area_sqft) || 0,
        VOLUME_CONTRIBUTION: 0,
        REVENUE_PER_SQFT: Number(store.revenue_per_sqft) || 0,

        // Ethnicity fields
        NATIONALS: Number(ethnicity.NATIONALS) || 0,
        EXPAT_ARAB: Number(ethnicity.EXPAT_ARAB) || 0,
        ISC: Number(ethnicity.ISC) || 0,
        SEAC: Number(ethnicity.SEAC) || 0,
        WESTERN: Number(ethnicity.WESTERN) || 0
      };
    });
    stores.value = processed;
  } catch (error) {
    console.error('Failed to fetch and process cluster data:', error);
  }
});
</script>


<style scoped>
/* Basic fade transition for cluster expand/collapse */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}

th,
td {
  white-space: nowrap;
}

</style>
