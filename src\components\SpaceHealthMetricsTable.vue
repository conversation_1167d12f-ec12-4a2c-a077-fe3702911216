<template>
  <div class="space-y-6">

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-2 text-gray-600">Loading data...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4">
      <p class="text-red-600">{{ error }}</p>
    </div>

    <!-- Table -->
    <div v-else class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="overflow-x-auto">
        <table class="w-full text-sm border border-gray-200 rounded-lg">
          <!-- Header -->
          <thead class="bg-gray-100 top-0 z-50">
            <!-- Main header groups -->
            <tr>
              <th v-for="(header, idx) in stickyHeaders" :key="`sticky-${idx}`"
                  :class="stickyHeaderClass(idx)"
                  :style="stickyHeaderStyle(idx)"
                  rowspan="2">
                {{ header }}
              </th>
              <th colspan="8" class="py-2 px-2 text-center text-xs font-bold text-tertiary uppercase tracking-wider border-r border-gray-300" >
                Area Metrics
              </th>
              <th colspan="4" class="py-2 px-2 text-center text-xs font-bold text-tertiary uppercase tracking-wider border-r border-gray-300" >
                Stock Metrics
              </th>
              <th colspan="4" class="py-2 px-2 text-center text-xs font-bold text-tertiary uppercase tracking-wider border-r border-gray-300" >
                Sales Metrics
              </th>
              <th colspan="8" class="py-2 px-2 text-center text-xs font-bold text-tertiary uppercase tracking-wider border-r border-gray-300" >
                Area Productivity
              </th>
              <th colspan="3" class="py-2 px-2 text-center text-xs font-bold text-tertiary uppercase tracking-wider border-r border-gray-300" >
                Inventory Health
              </th>
            </tr>
            <!-- Sub headers -->
            <tr>
              <th v-for="header in areaMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-blue-50 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in stockMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-green-50 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in salesMetricsHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-purple-50 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in areaProductivityHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-orange-50 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
              <th v-for="header in inventoryHealthHeaders" :key="header"
                  class="py-2 px-2 text-center text-xs font-bold bg-red-50 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 last:border-r-0 min-w-[80px]">
                {{ header }}
              </th>
            </tr>
          </thead>

          <!-- Body -->
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(row, index) in paginatedRows" :key="index" class="hover:bg-gray-50">
              <!-- Sticky columns -->
              <td v-for="(field, idx) in stickyFields" :key="`sticky-${idx}`"
                  :class="stickyCellClass(idx)"
                  :style="stickyCellStyle(idx)">
                {{ getFieldValue(row, field) }}
              </td>

              <!-- Area Metrics -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.lm) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sqft) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'lmRank') }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'sqftRank') }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.fixtureDensity, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.lmCont, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sqftCont, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.diff, 2) }}</td>

              <!-- Stock Metrics -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.optionCount) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.optionDensity, 2) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.sohQty) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.stockDensity, 2) }}</td>

              <!-- Sales Metrics -->
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revenue) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmv) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revenuePerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerDay) }}</td>

              <!-- Area Productivity -->
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revPerLmPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.revPerSqftPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerLmPerDay) }}</td>
              <td class="py-2 px-3 text-right border-r border-gray-200">{{ formatCurrency(row.gmvPerSqftPerDay) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'revPerLmPerDayRank') }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'revPerSqftPerDayRank') }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'gmvPerLmPerDayRank') }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ getFieldValue(row, 'gmvPerSqftPerDayRank') }}</td>

              <!-- Inventory Health -->
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.latestStock) }}</td>
              <td class="py-2 px-3 text-center border-r border-gray-200">{{ formatNumber(row.currentRos, 2) }}</td>
              <td class="py-2 px-3 text-center">{{ getFieldValue(row, 'coverDays') }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex justify-between items-center mt-4 p-4">
        <div class="text-sm text-gray-600">
          Page {{ currentPage }} of {{ totalPages }} ({{ totalRows }} total rows)
        </div>
        <div class="flex gap-2">
          <button
            @click="previousPage"
            :disabled="currentPage === 1"
            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            Previous
          </button>
          <button
            @click="nextPage"
            :disabled="currentPage === totalPages"
            class="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-300 text-white px-3 py-1 rounded text-sm transition-colors"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
    
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import Multiselect from '@vueform/multiselect'
import '@vueform/multiselect/themes/default.css'
import axios from 'axios'

// Data and state
const loading = ref(false)
const error = ref(null)
const originalRows = ref([])
const currentPage = ref(1)
const pageSize = 10
const totalRows = ref(0)
const nextPageUrl = ref(null)
const previousPageUrl = ref(null)

// Filters
const filters = ref({
  group: [],
  department: [],
  class: [],
  subClass: []
})

// Hierarchical filter options
const groupOptions = computed(() => {
  const allGroups = [...new Set(originalRows.value.map(r => r.group))];
  return allGroups.map(g => ({ value: g, label: g }));
});

const departmentOptions = computed(() => {
  let filtered = originalRows.value;
  if (filters.value.group.length > 0) {
    filtered = filtered.filter(r => filters.value.group.includes(r.group));
  }
  const allDepartments = [...new Set(filtered.map(r => r.department))];
  return allDepartments.map(d => ({ value: d, label: d }));
});

const classOptions = computed(() => {
  let filtered = originalRows.value;
  if (filters.value.group.length > 0) {
    filtered = filtered.filter(r => filters.value.group.includes(r.group));
  }
  if (filters.value.department.length > 0) {
    filtered = filtered.filter(r => filters.value.department.includes(r.department));
  }
  const allClasses = [...new Set(filtered.map(r => r.class))];
  return allClasses.map(c => ({ value: c, label: c }));
});

const subclassOptions = computed(() => {
  let filtered = originalRows.value;
  if (filters.value.group.length > 0) {
    filtered = filtered.filter(r => filters.value.group.includes(r.group));
  }
  if (filters.value.department.length > 0) {
    filtered = filtered.filter(r => filters.value.department.includes(r.department));
  }
  if (filters.value.class.length > 0) {
    filtered = filtered.filter(r => filters.value.class.includes(r.class));
  }
  const allSubclasses = [...new Set(filtered.map(r => r.subClass))];
  return allSubclasses.map(s => ({ value: s, label: s }));
});

// Filter options for template
const filterOptions = computed(() => ({
  group: {
    label: 'Group',
    options: groupOptions.value,
    placeholder: 'Select Groups'
  },
  department: {
    label: 'Department',
    options: departmentOptions.value,
    placeholder: 'Select Departments'
  },
  class: {
    label: 'Class',
    options: classOptions.value,
    placeholder: 'Select Classes'
  },
  subClass: {
    label: 'Sub Class',
    options: subclassOptions.value,
    placeholder: 'Select Sub Classes'
  }
}))

// Table structure
const stickyHeaders = ['Group', 'Department', 'Class', 'Sub Class']
const stickyFields = ['group', 'department', 'class', 'subClass']
const columnWidths = [120, 150, 120, 120] // Widths for sticky columns

const areaMetricsHeaders = ['LM', 'Sqft', 'LM Rank', 'Sqft Rank', 'Fixture Density', 'LM Cont', 'SQFT Cont', 'Diff']
const stockMetricsHeaders = ['Option Count', 'Option Density', 'SOH Qty', 'Stock Density']
const salesMetricsHeaders = ['Revenue', 'GMV', 'Revenue per day', 'GMV per day']
const areaProductivityHeaders = ['Rev per LM per day', 'Rev per Sqft per day', 'GMV per LM per day', 'GMV per Sqft per day', 'Rev per LM per day (Rank)', 'Rev per Sqft per day (Rank)', 'GMV per LM per day (Rank)', 'GMV per Sqft per day (Rank)']
const inventoryHealthHeaders = ['Latest Stock', 'Current ROS', 'Cover (days)']

// Computed properties
const stickyLeftPositions = computed(() => {
  const positions = [0]
  for (let i = 1; i < columnWidths.length; i++) {
    positions.push(positions[i - 1] + columnWidths[i - 1])
  }
  return positions
})

const totalPages = computed(() => Math.ceil(totalRows.value / pageSize))

const paginatedRows = computed(() => {
  return originalRows.value
})

// Methods
const stickyHeaderClass = (idx) => [
  'py-2 px-3 text-center text-xs font-bold bg-gray-200 text-tertiary uppercase tracking-wider whitespace-nowrap border-r border-gray-300 sticky z-40',
  idx === stickyHeaders.length - 1 ? 'border-r-2 border-gray-400' : ''
]

const stickyHeaderStyle = (idx) => ({
  left: stickyLeftPositions.value[idx] + 'px',
  minWidth: columnWidths[idx] + 'px'
})

const stickyCellClass = (idx) => [
  'py-2 px-3 text-center bg-white border-r border-gray-200 sticky z-30',
  idx === stickyFields.length - 1 ? 'border-r-2 border-gray-400' : ''
]

const stickyCellStyle = (idx) => ({
  left: stickyLeftPositions.value[idx] + 'px',
  minWidth: columnWidths[idx] + 'px'
})

const getFieldValue = (row, field) => {
  const value = row[field]
  return (value == null || value === '') ? '-' : value
}

const formatNumber = (value, decimals = 0) => {
  if (value == null || value === '') return '-'
  return Number(value).toLocaleString(undefined, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

const formatCurrency = (value) => {
  if (value == null || value === '') return '-'
  return new Intl.NumberFormat('en-AE', {
    style: 'currency',
    currency: 'AED',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

// Pagination
const nextPage = async () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    await loadData()
  }
}

const previousPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--
    await loadData()
  }
}

// Filtering
const applyFilters = async () => {
  currentPage.value = 1
  await loadData()
}

const clearFilters = async () => {
  Object.keys(filters.value).forEach(key => {
    filters.value[key] = []
  })
  currentPage.value = 1
  await loadData()
}

// Data loading
const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Build query parameters
    const params = new URLSearchParams()
    params.append('page', currentPage.value.toString())

    // Add filters
    if (filters.value.group.length > 0) {
      filters.value.group.forEach(g => params.append('grp_nm', g))
    }
    if (filters.value.department.length > 0) {
      filters.value.department.forEach(d => params.append('dpt_nm', d))
    }
    if (filters.value.class.length > 0) {
      filters.value.class.forEach(c => params.append('clss_nm', c))
    }
    if (filters.value.subClass.length > 0) {
      filters.value.subClass.forEach(s => params.append('sub_clss_nm', s))
    }

    const response = await axios.get(`scenario/health-metrics/?${params.toString()}`)
    const data = response.data

    // Map API data to component format
    const mappedData = data.results.map(item => ({
      group: item.grp_nm,
      department: item.dpt_nm,
      class: item.clss_nm,
      subClass: item.sub_clss_nm,

      // Area Metrics
      lm: item.lm,
      sqft: null, // Not provided in API
      lmRank: item.lm_rank,
      sqftRank: null, // Not provided in API
      fixtureDensity: null, // Not provided in API
      lmCont: item.lm_contribution_in_store,
      sqftCont: null, // Not provided in API
      diff: null, // Not provided in API

      // Stock Metrics
      optionCount: item.option_count,
      optionDensity: item.option_density,
      sohQty: item.soh,
      stockDensity: item.stock_density,

      // Sales Metrics
      revenue: item.rev,
      gmv: item.gmv,
      revenuePerDay: item.rev_per_day,
      gmvPerDay: item.gmv_per_day,

      // Area Productivity
      revPerLmPerDay: item.rev_per_lm_per_day,
      revPerSqftPerDay: null, // Not provided in API
      gmvPerLmPerDay: item.gmv_per_lm_per_day,
      gmvPerSqftPerDay: null, // Not provided in API
      revPerLmPerDayRank: item.rev_per_lm_per_day_rank,
      revPerSqftPerDayRank: null, // Not provided in API
      gmvPerLmPerDayRank: item.gmv_per_lm_per_day_rank,
      gmvPerSqftPerDayRank: null, // Not provided in API

      // Inventory Health
      latestStock: null, // Not provided in API
      currentRos: null, // Not provided in API
      coverDays: null // Not provided in API
    }))

    originalRows.value = mappedData
    totalRows.value = data.count
    nextPageUrl.value = data.next
    previousPageUrl.value = data.previous

  } catch (err) {
    error.value = 'Failed to load data: ' + err.message
  } finally {
    loading.value = false
  }
}

// Load all data for filter options
const loadAllDataForFilters = async () => {
  try {
    const response = await axios.get('scenario/health-metrics/?page_size=1000')
    const allData = response.data.results.map(item => ({
      group: item.grp_nm,
      department: item.dpt_nm,
      class: item.clss_nm,
      subClass: item.sub_clss_nm
    }))

    // Store all data for filter options
    originalRows.value = allData
  } catch (err) {
    console.error('Failed to load filter data:', err)
  }
}

// Expose methods to parent component
defineExpose({
  loadData,
  applyFilters,
  clearFilters
})

// Emit events
const emit = defineEmits(['loading'])

// Update loading function to emit events
const loadDataWithEmit = async () => {
  emit('loading', true)
  await loadData()
  emit('loading', false)
}

// Lifecycle
onMounted(async () => {
  await loadAllDataForFilters() // Load data for filters first
  await loadDataWithEmit() // Then load paginated data
})

// Watch for filter changes to enable dynamic filtering
watch(filters, () => {
  applyFilters()
}, { deep: true })
</script>

<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure sticky columns stay on top */
.sticky {
  position: sticky;
}

/* Multiselect styling */
.multiselect {
  min-height: 40px;
}
</style>
