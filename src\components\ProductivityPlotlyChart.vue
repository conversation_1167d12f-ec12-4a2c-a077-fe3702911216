<script setup lang="ts">
// @ts-expect-error: No types for plotly.js-dist-min
import Plotly from 'plotly.js-dist-min';
import { ref, onMounted, watch } from 'vue';

interface Point { x: number; y: number; }

const props = defineProps<{
  predictedData: Point[];
  originalData: Point[];
  r2: number;
  ridgeR2: number;
  diff: number;
  title: string;
  showModeBar?: boolean;
}>();

const plotId = ref('plotly-' + Math.random().toString(36).substr(2, 9));

function getParabolaVertex(data: Point[]): number {
  // Find the x where y is maximum
  let maxY = -Infinity;
  let maxX = data[0]?.x ?? 0;
  for (const p of data) {
    if (p.y > maxY) {
      maxY = p.y;
      maxX = p.x;
    }
  }
  return maxX;
}

function renderPlot() {
  const predictedTrace = {
    x: props.predictedData.map((p) => p.x),
    y: props.predictedData.map((p) => p.y),
    mode: 'lines',
    line: { color: 'blue', width: 3, shape: 'spline' },
    hoverinfo: 'none',
    showlegend: false,
    name: 'Predicted'
  };

  const originalTrace = {
    x: props.originalData.map((p) => p.x),
    y: props.originalData.map((p) => p.y),
    mode: 'markers',
    marker: {
      color: '#dc2626',
      size: 10,
      line: {
        color: '#fff',
        width: 2
      },
      symbol: 'circle'
    },
    hoverinfo: 'x+y',
    showlegend: false,
    name: 'Original'
  };

  const maximaX = getParabolaVertex(props.predictedData);

  const layout = {
    title: { 
      text: props.title, 
      font: { 
        size: 16, 
        family: 'SFProText',
        weight: 600
      }, 
      xref: 'paper', 
      x: 0.5 
    },
    xaxis: {
      title: 'Linear Meter (LM)',
      showline: true,
      linecolor: '#444',
      linewidth: 2,
      mirror: true
    },
    yaxis: {
      title: 'Predicted Productivity',
      showline: true,
      linecolor: '#444',
      linewidth: 2,
      mirror: true
    },
    margin: { t: 60, l: 60, r: 30, b: 60 },
    dragmode: 'zoom',
    showlegend: false,
    shapes: [
      {
        type: 'line',
        x0: maximaX,
        x1: maximaX,
        y0: Math.min(...props.predictedData.map(p => p.y)),
        y1: Math.max(...props.predictedData.map(p => p.y)),
        line: {
          color: '#059669',
          width: 2,
          dash: 'dash'
        }
      }
    ],
    annotations: [
      {
        x: maximaX,
        y: Math.max(...props.predictedData.map(p => p.y)),
        text: 'Peak',
        showarrow: false,
        font: {
          color: '#059669'
        },
        bgcolor: '#fff',
        bordercolor: '#059669',
        borderwidth: 1,
        borderpad: 4
      }
    ]
  };

  Plotly.newPlot(plotId.value, [predictedTrace, originalTrace], layout, {
    responsive: true,
    displayModeBar: !!props.showModeBar,
    staticPlot: false
  });
}

onMounted(renderPlot);
watch(props, renderPlot, { deep: true });
</script>

<template>
  <div>
    <div :id="plotId" style="width: 100%; height: 350px;"></div>
    <div class="bg-[#e5eaf1] text-center text-xl font-bold py-2 mt-2 rounded">
      R² : {{ r2 }} % | Ridge R² : {{ ridgeR2 }} % | difference : {{ diff }} %
    </div>
  </div>
</template>