<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import DynamicFilter from './common/DynamicFilter.vue'
import axios from 'axios'

const selectedStores = ref('')
const selectedGroup = ref<string[]>([])
const selectedDepartment = ref<string[]>([])
const selectedClasses = ref<string[]>([])
const selectedSubClass = ref<string[]>([])
const clearAllFilters = () => {
  selectedGroup.value = []
  selectedDepartment.value = []
  selectedClasses.value = []
  selectedSubClass.value = []
}
onMounted(async () => {
  getGdcsData()
})

interface GdcsDataItem {
  LOC_CD: string
  LOC_NM: string
  GRP_NM: string
  DPT_NM: string
  CLSS_NM: string
  SUB_CLSS_NM: string
  }

const scenarioDetails = {
  concept: 'hb',
  scenario_id: 1,
  gdcsData: ref<GdcsDataItem[]>([])
}
const getGdcsData = async () => {
  try {
    const response = await axios.post(`/scenario/getAllGDCSdata/`, {
      concept: scenarioDetails.concept,
      scenario_id: scenarioDetails.scenario_id
    })
    scenarioDetails.gdcsData.value = response.data.gdcs_data
    // Assign zeroth store code to selectedStores if available
    if (scenarioDetails.gdcsData.value.length > 0) {
      selectedStores.value = scenarioDetails.gdcsData.value[0].LOC_CD
    }
  } catch (err) {
    console.error('Error fetching Data:', err)
  }
}

const uniqueStores = computed(() => {
  const stores = scenarioDetails.gdcsData.value.map(item => ({
    value: item.LOC_CD,
    label: `${item.LOC_NM} (${item.LOC_CD})`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }

  const groups = [...new Set(filtered.map(item => item.GRP_NM))]
  return groups.map(group => ({ value: group, label: group }))
})

const uniqueDepartments = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.GRP_NM))
  }

  const departments = [...new Set(filtered.map(item => item.DPT_NM))]
  return departments.map(dept => ({ value: dept, label: dept }))
})

const uniqueClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.GRP_NM))
  }
  if (selectedDepartment.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartment.value.includes(item.DPT_NM))
  }

  const classes = [...new Set(filtered.map(item => item.CLSS_NM))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroup.value.length > 0) {
    filtered = filtered.filter(item => selectedGroup.value.includes(item.GRP_NM))
  }
  if (selectedDepartment.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartment.value.includes(item.DPT_NM))
  }
  if (selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.CLSS_NM))
  }

  const subClasses = [...new Set(filtered.map(item => item.SUB_CLSS_NM))]
  return subClasses.map(subCls => ({ value: subCls, label: subCls }))
})

</script>
<template>
  <div class="px-6 pt-6 w-full mx-auto">
    <div class="flex w-full flex-wrap gap-3 items-end">
      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Store Name</label>
        <DynamicFilter v-model="selectedStores" :multiselect="true" label="Store Names" placeholder="Select Stores"
          :options="uniqueStores" variant="outline" size="sm" />
      </div>

      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Groups</label>
        <DynamicFilter v-model="selectedGroup" :multiselect="true" label="Groups" placeholder="Select Groups"
          :options="uniqueGroups" variant="outline" size="sm" />
      </div>

      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Department</label>
        <DynamicFilter v-model="selectedDepartment" :multiselect="false" label="Department"
          placeholder="Select Department" :options="uniqueDepartments" variant="outline" size="sm" />
      </div>

      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Class</label>
        <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Class" placeholder="Select Class"
          :options="uniqueClasses" variant="outline" size="sm" />
      </div>

      <div class="space-y-1 flex-1 min-w-[12%]">
        <label class="block text-xs font-medium text-gray-600">Sub Class</label>
        <DynamicFilter v-model="selectedSubClass" :multiselect="true" label="Sub Class" placeholder="Select Sub Class"
          :options="uniqueSubClasses" variant="outline" size="sm" />
      </div>

      <div class="space-y-1 flex-1 min-w-[12%]">
        <button
          class="flex items-center gap-2 px-3 py-2 text-xs bg-tertiary text-white rounded hover:bg-green-900 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2a1 1 0 01-.293.707L14 13.414V19a1 1 0 01-.553.894l-4 2A1 1 0 018 21v-7.586L3.293 6.707A1 1 0 013 6V4z" />
          </svg>
          Apply Filters
        </button>
      </div>

      <div class="items-center flex-1 min-w-[12%]">
        <button
          class="px-4 py-2 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100"
          @click="clearAllFilters">
          Clear All
        </button>
      </div>
    </div>
  </div>
</template>
