<template>
  <div class="flex h-screen overflow-hidden">
    <div
      class="transition-all duration-300 bg-gray-50 relative font-sans flex-shrink-0"
      :class="isExpanded ? 'w-72' : 'w-20'"
    >
      <Sidebar
        v-if="shouldShowSidebar"
        :isExpanded="isExpanded"
        @mouseover="expandSidebar"
        @mouseleave="collapseSidebar"
      />
    </div>
    <div class="flex-1 flex flex-col min-w-0 overflow-hidden">
      <conceptBar class="flex-shrink-0" v-if="shouldShowHomePage" />
      <div class="flex-1 overflow-auto">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import Sidebar from './components/Sidebar.vue';
import conceptBar from './components/ConceptBar.vue';

const isExpanded = ref(false);
const route = useRoute();

const shouldShowSidebar = computed(() => route.path !== '/' && route.path !== '/concept-selection');
const shouldShowHomePage = computed(() => route.path !== '/' && route.path !== '/callback');

const expandSidebar = () => {
  isExpanded.value = true;
};

const collapseSidebar = () => {
  isExpanded.value = false;
};
</script>