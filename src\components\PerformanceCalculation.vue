<template>
    <div class="flex w-full">
        <div class="flex-1 flex flex-col overflow-hidden">
            <div class="bg-primary">
                <div class="pl-12 pb-2">
                    <div class="">
                        <FilterBarTest />
                    </div>
                </div>
            </div>
            <main class="flex-1 p-4 sm:p-6 lg:p-8">
                <!-- Loading overlay -->
                <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                        <span class="text-gray-700">Updating chart...</span>
                    </div>
                </div>
                
                
                <div class="flex w-full">
                   <div class="h-[30vw] relative w-[70%]">
                      <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
                  </div>

                <div class="flex flex-col items-center gap-2 w-[30%]">
                  
              <div v-for="cfg in labelConfigs" :key="cfg.key" class="flex items-center gap-2">
                <span class="w-28">{{ cfg.name }}</span>
                <input
                  type="range"
                  min="0"
                  max="100"
                  v-model="sliderValues[cfg.key]"
                  step="1"
                  class="w-24"
                  :style="{ accentColor: cfg.color }"
                />
                <span>{{ Math.round(sliderValues[cfg.key]) }}%</span>
              </div>
            </div>

                  
                 
                </div>          
                
                <div class="rounded-lg shadow p-4">
                    <!-- Horizontal scroll wrapper -->
                    <div class="overflow-x-auto">
                        <!-- Minimum width triggers horizontal scroll -->
                        <div class="overflow-y-auto relative">
                            <table class="w-full text-sm text-left border border-[#60A5FA] bg-white">
                                <thead class="bg-primary">
                                    <tr>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Store</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Subclass</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Linear meter</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Units per invoice (UPI)</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Customer Penetration (CP)</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Cover</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Margin %</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Productivity</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">ASP</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Performance</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Performance Category</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">LM Category</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Combined Category</th>
                                        <th class="px-3 py-2 border sticky top-0 z-20">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(row, index) in paginatedData" :key="index">
                                    <td class="px-3 py-2 border">{{ row.store }}</td>
                                    <td class="px-3 py-2 border">{{ row.subClass }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.LM }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.UPI }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.CP }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.cover }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.marginPercent }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.productivity }}</td>
                                    <td class="px-3 py-2 border text-right">{{ row.asp }}</td>
                                    <td class="px-3 py-2 border text-right">{{ row.performance }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.performanceCategory }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.categoryLM }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.combinedCategory }}</td>
                                    <td class="px-3 py-2 border text-center">{{ row.action }}</td>
                                    </tr>
                                    <!-- Pagination Controls -->
                                    <tr>
                                    <td colspan="14" class="px-3 py-2 border text-center">
                                    <button class="run-button mr-2" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">Previous</button>
                                    <span>Page {{ currentPage }} of {{ Math.max(1, Math.ceil(totalRows / rowsPerPage)) }} (total {{ totalRows }} rows)</span>
                                    <button class="run-button ml-2" :disabled="currentPage === Math.ceil(totalRows / rowsPerPage) || totalRows === 0" @click="goToPage(currentPage + 1)">Next</button>
                                    </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, nextTick, onUnmounted,computed } from 'vue';
import FilterBar from './FilterBar.vue';
import * as echarts from 'echarts';
import FilterBarTest from './FilterBarTest.vue';

const chartContainer = ref(null)
let myChart = null
const isLoading = ref(false)

const labelConfigs = [
  { name: 'Units per invoice', color: '#166534', key: 'upi' },
  { name: 'Customer Penetration', color: '#15803D', key: 'cp' },
  { name: 'Store Cover', color: '#22C55E', key: 'cover' },
  { name: 'Margin %', color: '#4ADE80', key: 'margin' },
  { name: 'Productivity', color: '#86EFAC', key: 'productivity' },
  { name: 'Average Selling Price', color: '#DCFCE7', key: 'asp' }
];

const labels = labelConfigs.map(item => item.name);
const backgroundColors = labelConfigs.map(item => item.color);

// Calculate default value as equal distribution
const defaultValue = 100 / labels.length
const dataValues = ref(Array(labels.length).fill(defaultValue))

const sliderValues = ref({});

labelConfigs.forEach(cfg => {
  sliderValues.value[cfg.key] = defaultValue;
});

// Initialize slider values with default value
const upi = ref(Math.round(defaultValue));
const cp = ref(Math.round(defaultValue));
const cover = ref(Math.round(defaultValue));
const margin = ref(Math.round(defaultValue));
const productivity = ref(Math.round(defaultValue));
const asp = ref(Math.round(defaultValue));

// Debounce timeout for smooth updates
let updateTimeout = null

// Initialize chart
onMounted(() => {
  if (!chartContainer.value) {
    console.error('Chart container not found.')
    return
  }

  // Update initial dataValues with slider values
  dataValues.value = [upi.value, cp.value, cover.value, margin.value, productivity.value, asp.value]

  // Initialize ECharts
  myChart = echarts.init(chartContainer.value)

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}% ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '5%',
      left: 'center'
    },
    series: [
      {
        name: 'Performance Metrics',
        type: 'pie',
        radius: '70%',
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'inside',
          formatter: '{c}%',
          fontSize: 12,
          fontWeight: 'bold',
          color: '#000'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: labelConfigs.map((cfg, index) => ({
          value: dataValues.value[index],
          name: cfg.name,
          itemStyle: {
            color: cfg.color
          }
        })),
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: function (idx) {
          return Math.random() * 200;
        }
      }
    ]
  }

  myChart.setOption(option)

  // Handle window resize
  window.addEventListener('resize', () => {
    myChart?.resize()
  })
})

// Cleanup on unmount
onUnmounted(() => {
  if (myChart) {
    myChart.dispose()
  }
  window.removeEventListener('resize', () => {
    myChart?.resize()
  })
})

// Debounced update
const updateChart = () => {
  if (updateTimeout) clearTimeout(updateTimeout);
  isLoading.value = true;

  updateTimeout = setTimeout(async () => {
    await nextTick();
    dataValues.value = labelConfigs.map(cfg => Math.round(sliderValues.value[cfg.key]));

    if (myChart) {
      const newData = labelConfigs.map((cfg, index) => ({
        value: dataValues.value[index],
        name: cfg.name,
        itemStyle: {
          color: cfg.color
        }
      }));

      myChart.setOption({
        series: [{
          data: newData
        }]
      });
    }

    setTimeout(() => {
      isLoading.value = false;
    }, 200);
  }, 100);
};

// Watch all slider values
watch(sliderValues, updateChart, { deep: true });


import { baseFastapiURL } from '../main';

const tableData = ref([]);
const currentPage = ref(1);
const rowsPerPage = 10;
const totalRows = ref(0);

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  return tableData.value.slice(start, start + rowsPerPage);
});

const fetchPerformanceData = async () => {
  isLoading.value = true;
  try {
    const response = await fetch(`${baseFastapiURL}/fetch-performance-data`);
    const json = await response.json();
    // Map API fields to table columns
    tableData.value = json.map(row => ({
      store: row.LOC_CD,
      subClass: row.SUB_CLSS_NM,
      LM: row.TOTAL_LM_AVG,
      UPI: row.UNITS_PER_INV,
      CP: row.CUST_PEN,
      cover: row.COVER,
      marginPercent: row.MARGIN_PERC,
      productivity: row.GMV_PER_LM,
      asp: row.ASP,
      performance: row.Performance,
      performanceCategory: row.perf_bucket,
      categoryLM: row.lm_bucket,
      combinedCategory: `${row.perf_bucket}${row.lm_bucket}`,
      action: row.action,
    }));
    totalRows.value = tableData.value.length;
  } catch (e) {
    console.error('Failed to fetch performance data', e);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchPerformanceData();
});

function goToPage(page: number) {
  if (page < 1 || page > Math.ceil(totalRows.value / rowsPerPage)) return;
  currentPage.value = page;
}
</script>

<style scoped>
.run-button {
    padding: 10px 16px;
    background-color: #3B82F6;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

/* Custom loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}
</style>