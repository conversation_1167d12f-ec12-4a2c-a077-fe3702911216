<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  item: any;
}

const props = defineProps<Props>();

// Format number with commas for thousands
const formatNumber = (value: number): string => {
  return value.toLocaleString();
};

// Format decimal with 2 digits
const formatDecimal = (value: number): string => {
  return value.toFixed(2);
};

// Format percentage
const formatPercent = (value: number): string => {
  return value.toFixed(1) + '%';
};

// Compute CSS classes for recommendation
const recommendationClass = computed(() => {
  switch (props.item.recommendation) {
    case 'Increase': return 'bg-green-100 text-green-800';
    case 'Decrease': return 'bg-red-100 text-red-800';
    default: return 'bg-yellow-100 text-yellow-800';
  }
});

// Compute CSS classes for GMV change
const gmvChangeClass = computed(() => {
  if (props.item.gmvChange > 0) return 'text-positive';
  if (props.item.gmvChange < 0) return 'text-negative';
  return '';
});

// Compute CSS classes for space change
const spaceChangeClass = computed(() => {
  if (props.item.spaceChange > 0) return 'text-positive';
  if (props.item.spaceChange < 0) return 'text-negative';
  return '';
});

// Compute background color based on productivity

const productivityClass = computed(() => {
  if (props.item.recommendation == 'Keep') return 'bg-yellow-100 text-yellow-800';
  if (props.item.recommendation == 'Increase') return 'bg-green-100 text-green-800';
  if (props.item.recommendation == 'Decrease') return 'bg-red-100 text-red-800';
  return '';
});

</script>

<template>
  <tr class="hover:bg-gray-50 transition-colors">
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.storeCode }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.storeName }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.group }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.department }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.class }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      {{ item.subclass }}
    </td>
    <td :class="['px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right', productivityClass]">
      {{ item.productivity }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ item.gmvLmRank }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ item.spaceRank }}
    </td>
    <td class="px-6 py-3 text-sm whitespace-nowrap">
      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="recommendationClass">
        {{ item.recommendation }}
      </span>
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(item.min) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(item.max) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(item.currentLm) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(item.optimizedLm) }}
    </td>
    <td class="px-6 py-3 text-sm whitespace-nowrap text-right" :class="spaceChangeClass">
      {{ formatPercent(item.spaceChange) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatNumber(item.optimizedGmv) }}
    </td>
    <td class="px-6 py-3 text-sm whitespace-nowrap text-right" :class="gmvChangeClass">
      {{ formatPercent(item.gmvChange) }}
    </td>
  </tr>
</template>