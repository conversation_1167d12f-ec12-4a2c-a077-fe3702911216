<script setup lang="ts">
import { ref } from 'vue';

const groupOptions = ref(['Fashion', 'Home', 'Electronics']);
const departmentOptions = ref(['Bags', 'Fashion Accessories', 'Hair Accessories', 'Bathroom', 'Floor Covering', 'Home Decor', 'Soft Furnishing', 'Tabletop']);
const classOptions = ref(['Class A', 'Class B', 'Class C']);
const subClassOptions = ref(['Subclass 1', 'Subclass 2', 'Subclass 3']);
const storeNameOptions = ref(['Dalma Mall', 'Dalma Mall', 'Dalma Mall']);
const storeIdOptions = ref(['21404', '21404', '21404']);

const selectedGroup = ref('');
const selectedDepartment = ref('');
const selectedClass = ref('');
const selectedSubClass = ref('');
const selectedStoreName = ref('');
const selectedStoreId = ref('');
const startDate = ref('');
const endDate = ref('');

const dropdowns = [
  { label: 'Store ID', model: selectedStoreId, options: storeIdOptions.value },
  { label: 'Store', model: selectedStoreId, options: storeNameOptions.value },
  { label: 'Group', model: selectedGroup, options: groupOptions.value },
  { label: 'Department', model: selectedDepartment, options: departmentOptions.value },
  { label: 'Class', model: selectedClass, options: classOptions.value },
  { label: 'Sub Class', model: selectedSubClass, options: subClassOptions.value },
];

const applyFilters = () => {
  console.log('Applying filters...');
};
</script>

<template>
  <div class="flex flex-wrap items-center gap-3 mb-3 ml-16" >
    <div v-for="(dropdown, idx) in dropdowns" :key="idx" class="relative">
      <div class="flex items-center bg-primary h-8 px-2 text-sm text-gray-800 font-normal rounded cursor-pointer border border-secondary">
        <span class="mr-1 text-tertiary">{{ dropdown.label }}</span>
        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7"/></svg>
        <select v-model="dropdown.model" class="absolute left-0 top-0 w-full h-full opacity-0 cursor-pointer">
          <option v-for="option in dropdown.options" :key="option" :value="option">{{ option }}</option>
        </select>
      </div>
    </div>
    <button @click="applyFilters" class="h-9 px-4 bg-secondary text-white text-sm font-semibold shadow-sm hover:bg-tertiary rounded ml-3 transition-colors " >
      Filter
    </button>
  </div>
</template>
