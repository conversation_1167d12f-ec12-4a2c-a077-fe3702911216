<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { baseFastapiURL } from '../main';

const tableData = ref([]);
const currentPage = ref(1);
const rowsPerPage = 10;
const totalRows = ref(0);
const isLoading = ref(false);

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * rowsPerPage;
  return tableData.value.slice(start, start + rowsPerPage);
});

const fetchOptimizationData = async () => {
  isLoading.value = true;
  try {
    const response = await fetch(`${baseFastapiURL}/fetch-optimization-data`);
    const json = await response.json();
    tableData.value = json.map(row => ({
      storeCode: row.LOC_CD,
      group: row.GRP_NM,
      department: row.DPT_NM,
      class: row.CLSS_NM,
      subclass: row.SUB_CLSS_NM,
      currentLm: row.current_lm,
      min: row.MIN_LM,
      max: row.max_sat_lm,
      optimizedLm: row.optimized_lm,
      spaceChange: row.lm_delta,
      spaceChangePercent: row.space_change_percent,
      currentRevenue: row.new_metric,
      optimizedRevenue: row.NET_SLS_AMT_sum_reference_month,
      revenueChangePercent: row.metric_change_percent,
      recommendation: row.Final_Action,
      changeInOptions: row.optimized_no_of_options,
      changeInQty: row.optimized_qty
    }));
    totalRows.value = tableData.value.length;
  } catch (e) {
    console.error('Failed to fetch optimization data', e);
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchOptimizationData();
});

function goToPage(page: number) {
  if (page < 1 || page > Math.ceil(totalRows.value / rowsPerPage)) return;
  currentPage.value = page;
}
</script>

<template>
  <div class="overflow-x-auto">
    <table class="divide-y border border-table-border rounded-lg">
      <thead class="bg-table-header">
        <tr>
          <th v-for="(header, index) in [
            'Store Code', 'Group', 'Department', 'Class', 'Subclass','Current LM' ,'Min',
            'Max','Optimized LM','Space Change','Space Change %','Current Revenue',   'Optimized Revenue',
            'Revenue Change%','Recommendation','Change in Options', 'Change in Qty'
          ]"
          :key="index"
          class="px-2 py-3 text-sm font-bold uppercase tracking-wider cursor-pointer border border-table-border text-center bg-table-header"
          :class="{ 'text-right': index >= 6 && index !== 9 && index !== 14 && index !== 16 }"
          @click="index !== 14 && index !== 16 ? sortData(header.toLowerCase()) : undefined"
          >
            <span>{{ header }}</span>
            <!-- Edit/Save icon for Space Change -->
            <template v-if="header === 'Space Change'">
              <button
                v-if="editColumn !== 'spaceChange'"
                @click.stop="startEditColumn('spaceChange')"
                class="ml-2"
                title="Edit Space Change"
              >
                <!-- Pencil SVG icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2a2 2 0 012 2v2h6"/></svg>
              </button>
              <button
                v-else
                @click.stop="saveEditColumn('spaceChange')"
                class="ml-2"
                title="Save Space Change"
              >
                <!-- Check SVG icon -->
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              </button>
            </template>
            <!-- Edit/Save icon for GMV Change -->
            <template v-if="header === 'GMV Change'">
              <button
                v-if="editColumn !== 'gmvChange'"
                @click.stop="startEditColumn('gmvChange')"
                class="ml-2"
                title="Edit GMV Change"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.586-6.586a2 2 0 112.828 2.828L11.828 15.828a2 2 0 01-2.828 0L9 13zm-6 6h6v-2a2 2 0 012-2h2a2 2 0 012 2v2h6"/></svg>
              </button>
              <button
                v-else
                @click.stop="saveEditColumn('gmvChange')"
                class="ml-2"
                title="Save GMV Change"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="inline w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>
              </button>
            </template>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, idx) in paginatedData" :key="`${item.storeCode}-${item.department}-${idx}`">
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.storeCode }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.group }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.department }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.class }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.subclass }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.currentLm }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.min }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.max }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.optimizedLm }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.spaceChange }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.spaceChangePercent }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.currentRevenue }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.optimizedRevenue }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.revenueChangePercent }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-center">{{ item.recommendation }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.changeInOptions }}</td>
          <td class="px-2 py-2 text-sm border border-table-border text-right">{{ item.changeInQty }}</td>
        </tr>
        <!-- Pagination Controls -->
        <tr>
          <td colspan="17" class="px-2 py-2 border text-center">
            <button class="run-button mr-2" :disabled="currentPage === 1" @click="goToPage(currentPage - 1)">Previous</button>
            <span>Page {{ currentPage }} of {{ Math.max(1, Math.ceil(totalRows / rowsPerPage)) }} (total {{ totalRows }} rows)</span>
            <button class="run-button ml-2" :disabled="currentPage === Math.ceil(totalRows / rowsPerPage) || totalRows === 0" @click="goToPage(currentPage + 1)">Next</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background-color 0.2s;
}

th:hover {
  /* background-color: #E2E8F0; */
}
</style>