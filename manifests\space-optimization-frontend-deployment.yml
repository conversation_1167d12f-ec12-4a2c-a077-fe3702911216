apiVersion: apps/v1
kind: Deployment
metadata:
  name: space-optimization-frontend-app
  namespace: dll-space-optimization
spec:
  replicas: 1
  selector:
    matchLabels:
      app: space-optimization-frontend-app
  template:
    metadata:
      labels:
        app: space-optimization-frontend-app
    spec:
      containers:
        - name: space-optimization-frontend-app
          image: lmapaz1acrdllprd02.azurecr.io/dll-space-optimization/optimization-frontend-img
          ports:
            - containerPort: 80