<script setup>
import { ref, computed, onMounted, customRef, watch } from 'vue'
import Multiselect from 'vue-multiselect'
import axios from 'axios'
import { API_BASE_PATH } from '../constant.js'
import DynamicFilter from './common/DynamicFilter.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { useScenarioStore } from '../stores/ScenarioStore.js'

const stepsStore = useStepsStore()
const scenarioStore = useScenarioStore()
// Call API when component is mounted
onMounted(() => {
  fetchTestAndControlStores()
})

const scenarioDetails = {
  concept: 'hb',
  territory: 'ae',
  scenario_id: ref(null),
  testAndControlStore: ref(null)

}

const storeOnSimilarity = ref([])

onMounted(() => {
  const storedData = stepsStore.getScenarioData;
  if (storedData && storedData.scenario_id) {
    scenarioDetails.scenario_id.value = storedData.scenario_id;
  }
});

const storeMap = () => {
  const map = {}
  scenarioDetails.testAndControlStore.value.forEach(store => {
    map[store.loc_cd] = {
      loc_nm: store.loc_nm,
      cluster_num: store.cluster_num
    }
  })
  return map
}

// Flattened data for table
const storePairs = () => {
  storeOnSimilarity.value = []

  scenarioDetails.testAndControlStore.value.forEach(testStore => {
    if (!testStore.similarity_scores) return

    const scores = Object.entries(testStore.similarity_scores)

    // Sort scores descending by similarity
    scores.sort((a, b) => b[1] - a[1])

    for (const [controlCd, score] of scores) {
      const control = storeMap()[controlCd]
      if (!control) continue

      storeOnSimilarity.value.push({
        testStore: `${testStore.loc_cd} - ${testStore.loc_nm}`,
        controlStore: `${controlCd} - ${control.loc_nm}`,
        similarityScore: score.toFixed(2),
        testCluster: testStore.cluster_num,
        controlCluster: control.cluster_num
      })
    }
  })
}

const fetchTestAndControlStores = async () => {
  try {
    const response = await axios.post(`${API_BASE_PATH}/scenario/testAndControlStore/`, {
      concept: scenarioDetails.concept,
      territory: scenarioDetails.territory
    })

    scenarioDetails.testAndControlStore.value = response.data
    storePairs()
  } catch (err) {
    console.error('Error fetching test and control stores:', err)
  }
}
const insertTestControlStores = async () => {
  if(testControlMappings.value.length === 0) {
    alert("Please select at least one test and control store mapping before proceeding.")
    return
  }
  try {
    const formData = stepsStore.getScenarioData
    formData.storeSelection = testControlMappings.value
    console.log("Form Data:", formData)
    const response = await axios.post(`${API_BASE_PATH}/scenario/insertTestControlStr/`, {
      store_codes: JSON.stringify(testControlMappings.value),
      // scenario_id: scenarioDetails.scenario_id.value
      scenario_id : scenarioStore.getScenarioId
    })

    scenarioDetails.testAndControlStore.value = response.data
    stepsStore.goToNextStep()
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}

const selectedTestStores = ref([])
const selectedControlStores = ref([])
const testControlMappings = ref([])
const uniqueTestStores = () => {
  const data = scenarioDetails.testAndControlStore?.value
  if (!Array.isArray(data) || data.length === 0) return []

  // Step 1: Extract test and control store codes into a Set
  const excludedStores = new Set()
  if (testControlMappings.value.length > 0) {
    testControlMappings.value.forEach(mapping => {
      if (mapping.testStore) excludedStores.add(mapping.testStore)
      if (Array.isArray(mapping.controlStores)) {
        mapping.controlStores.forEach(store => excludedStores.add(store))
      }
    })
  }
  // Step 2: Filter duplicates and excluded stores
  const seen = new Set()
  const filterData = data.filter(store => {
    const locCode = store.loc_cd
    if (!seen.has(locCode) && !excludedStores.has(locCode)) {
      seen.add(locCode)
      return true
    }
    return false
  }).map(store => ({
    value: `${store.loc_cd}`,
    label: `${store.loc_nm} - ${store.loc_cd}`
  }))

  return filterData
}


const getAvailableControlStores = () => {
  const data = scenarioDetails.testAndControlStore?.value
  if (!Array.isArray(data) || data.length === 0) return []

  // Collect all test and control stores into a Set to exclude
  const excludedStores = new Set()
  for (const mapping of testControlMappings.value || []) {
    if (mapping.testStore) excludedStores.add(mapping.testStore)
    if (Array.isArray(mapping.controlStores)) {
      for (const store of mapping.controlStores) {
        excludedStores.add(store)
      }
    }
  }

  return data
    .filter(store =>
      !excludedStores.has(store.loc_cd) && !selectedTestStores?.value?.includes(store.loc_cd)
    )
    .map(store => ({
      value: store.loc_cd,
      label: `${store.loc_nm} - ${store.loc_cd}`
    }))
}



function customStoreLabel(option) {
  return `${option.loc_cd} - ${option.loc_nm}`
}

function getAllSelectedLocCds() {
  if (!selectedTestStores.value || selectedControlStores.value.length === 0) {
    alert("Please select both test store and control store(s)")
    return
  }
  testControlMappings.value.push({
    testStore: selectedTestStores.value,
    controlStores: [...selectedControlStores.value]
  })
  // Generate store comparison data
  generateStoreComparisonData()
  // Reset selections for next mapping
  selectedTestStores.value = null
  selectedControlStores.value = []
}


// Reactive data for store comparison
const storeComparisonData = ref([])

// Computed properties for dynamic headers
const ethnicityKeys = computed(() => {
  const allKeys = new Set()
  storeComparisonData.value.forEach(store => {
    if (store.ethnicity_contribution) {
      Object.keys(store.ethnicity_contribution).forEach(key => allKeys.add(key))
    }
  })
  return Array.from(allKeys).sort()
})

const volumeKeys = computed(() => {
  const allKeys = new Set()
  storeComparisonData.value.forEach(store => {
    if (store.volume_contribution) {
      Object.keys(store.volume_contribution).forEach(key => allKeys.add(key))
    }
  })
  return Array.from(allKeys).sort()
})

// Methods

function generateStoreComparisonData() {
  const comparisonData = []

  testControlMappings.value.forEach(mapping => {
    // Get all store codes from mapping
    const allStoreCodes = [mapping.testStore, ...mapping.controlStores]
    const uniqueStoreCodes = [...new Set(allStoreCodes)] // Remove duplicates

    uniqueStoreCodes.forEach(storeCode => {
      // Find store data from scenarioDetails
      const storeData = scenarioDetails.testAndControlStore.value.find(
        store => store.loc_cd === storeCode
      )

      if (storeData) {
        // Determine store type
        let storeType = 'Control Store'
        if (mapping.testStore.includes(storeCode)) {
          storeType = 'Test Store'
        }

        // Add store type to the data
        comparisonData.push({
          ...storeData,
          storeType: storeType
        })
      }
    })
  })

  storeComparisonData.value = comparisonData
}

// Helper methods
function formatNumber(value) {
  if (value === null || value === undefined) return '-'
  return new Intl.NumberFormat().format(value)
}

function formatVolumeKey(key) {
  // Convert snake_case to Title Case
  return key.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

// Watch for changes in testControlMappings to regenerate data
watch(testControlMappings, () => {
  generateStoreComparisonData()
}, { deep: true })
</script>

<template>
  <div class="px-10 py-2 space-y-2">
    <!-- Top Section: Test & Control Store List -->
    <h3 class="text-lg font-semibold text-tertiary mb-2">Test & Control Store List</h3>
    <div class="bg-white rounded-2xl shadow px-4 pb-2 h-[40vh] overflow-y-auto custom-scroll">

      <table class="w-full text-sm text-left">
        <thead class="text-xs sticky top-0 bg-white z-10">
          <tr>
            <th class="px-4 py-2">Test Store</th>
            <th class="px-4 py-2">Control Store</th>
            <th class="px-4 py-2">Similarity Score</th>
            <th class="px-4 py-2">Test Cluster</th>
            <th class="px-4 py-2">Control Cluster</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(pair, index) in storeOnSimilarity" :key="index" class="border-t">
            <td class="px-4 py-1">{{ pair.testStore }}</td>
            <td class="px-4 py-1">{{ pair.controlStore }}</td>
            <td class="px-4 py-1">{{ pair.similarityScore }}</td>
            <td class="px-4 py-1">{{ pair.testCluster }}</td>
            <td class="px-4 py-1">{{ pair.controlCluster }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Store Selection Dropdowns -->
    <div class="text-tertiary font-semibold">
      <h3>Select Test and Control Stores :</h3>
    </div>
    <div class="ml-2 flex items-center gap-4">
      <div class="space-y-1 w-1/3">
        <label class="block text-xs font-medium text-gray-600">Test Store</label>
        <DynamicFilter v-model="selectedTestStores" :multiselect="false" label="Groups" placeholder="Test Stores"
          :options="uniqueTestStores()" variant="secondary" size="sm" />
      </div>
      <div class="space-y-1 w-1/3">
        <label class="block text-xs font-medium text-gray-600">Control Store</label>
        <DynamicFilter v-model="selectedControlStores" :multiselect="true" label="Groups" :max-selections=2
          placeholder="Test Stores" :options="getAvailableControlStores()" variant="secondary" size="sm" />
      </div>
      <div class="w-1/3 mt-4">
        <button @click="getAllSelectedLocCds()"
          class="bg-[#16A34A] hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-200">
          Approve
        </button>
      </div>
    </div>

    <!-- Bottom Section: Store Comparison Metrics -->
    <div>
      <h3 class="font-semibold text-tertiary mb-4">Store Comparison Metrics</h3>

      <div v-if="storeComparisonData.length > 0" class="bg-white rounded-2xl shadow overflow-y-auto custom-scroll">
        <table class="min-w-full">
          <!-- Main Headers -->
          <thead>
            <tr class="bg-gray-50 text-sm">
              <th class=" px-4 py-2 text-left font-medium">Location Code</th>
              <th class=" px-4 py-2 text-left font-medium">Location Name</th>
              <th class=" px-4 py-2 text-left font-medium">Region</th>
              <th class=" px-4 py-2 text-left font-medium">Store Type</th>
              <th class=" px-4 py-2 text-left font-medium">Revenue</th>
              <th class=" px-4 py-2 text-left font-medium">Units</th>
              <th class=" px-4 py-2 text-left font-medium">GMV</th>
              <th class=" px-4 py-2 text-left font-medium">Total Customers</th>
              <!-- Dynamic Ethnicity Headers -->
              <th v-if="ethnicityKeys.length > 0" :colspan="ethnicityKeys.length"
                class=" px-4 py-2 text-center font-medium bg-gray-100">
                Ethnicity Contribution
              </th>
              <!-- Dynamic Volume Headers -->
              <th v-if="volumeKeys.length > 0" :colspan="volumeKeys.length"
                class=" px-4 py-2 text-center font-medium bg-gray-100">
                Volume Contribution
              </th>
            </tr>
            <!-- Sub Headers -->
            <tr class="bg-gray-50 text-sm">
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <th class=""></th>
              <!-- Ethnicity Sub Headers -->
              <th v-for="key in ethnicityKeys" :key="'eth-' + key" class="px-2 py-1 text-xs font-medium">
                {{ key }}
              </th>
              <!-- Volume Sub Headers -->
              <th v-for="key in volumeKeys" :key="'vol-' + key" class=" px-2 py-1 text-xs font-medium">
                {{ formatVolumeKey(key) }}
              </th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody>
            <tr v-for="store in storeComparisonData" :key="store.loc_cd" class="hover:bg-gray-50 text-sm border-t">
              <td class=" px-4 py-2">{{ store.loc_cd }}</td>
              <td class=" px-4 py-2">{{ store.loc_nm }}</td>
              <td class=" px-4 py-2">{{ store.rgn_nm }}</td>
              <td class=" px-4 py-2">{{ store.storeType }}</td>
              <td class=" px-4 py-2">{{ formatNumber(store.revenue) }}</td>
              <td class=" px-4 py-2">{{ formatNumber(store.units) }}</td>
              <td class=" px-4 py-2">{{ formatNumber(store.gmv) }}</td>
              <td class=" px-4 py-2">{{ formatNumber(store.total_customer) }}</td>
              <!-- Ethnicity Data -->
              <td v-for="key in ethnicityKeys" :key="'eth-data-' + key" class="px-2 py-2 text-center">
                {{ store.ethnicity_contribution[key] || '-' }}
              </td>
              <!-- Volume Data -->
              <td v-for="key in volumeKeys" :key="'vol-data-' + key" class=" px-2 py-2 text-center">
                {{ store.volume_contribution[key] || '-' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div v-else class="text-gray-500 text-center py-8">
        No store comparison data available. Please select and approve test and control stores.
      </div>
    </div>
  </div>
  <div class="flex justify-end m-4">
    <button @click="insertTestControlStores()" class="bg-tertiary text-white hover:bg-green-900 px-6 py-2 rounded "
      :disabled="stepsStore.currentStep === stepsStore.steps.length - 1">
      Next →
    </button>
  </div>
</template>

<style>
.custom-scroll::-webkit-scrollbar {
  width: 6px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  /* Customize color */
  border-radius: 9999px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scroll {
  scrollbar-width: thin;
  /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
  /* Firefox */
}
</style>