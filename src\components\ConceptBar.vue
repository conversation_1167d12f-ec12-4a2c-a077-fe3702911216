<template>
  <div class="flex w-full justify-end">
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top Bar -->
      <div class="bg-white flex justify-end pr-8 pt-2 pb-2">
        <!-- User and Concept Info -->
        <div class="flex items-center">
          <span v-if="firstName" class="flex font-semibold text-base mr-4">
            Hi, {{ firstName }}
          </span>
          <div v-if="conceptObj" class="flex items-center">
            <img
              :src="conceptObj.img"
              :alt="conceptObj.name"
              class="h-8 w-8 object-contain bg-white rounded-full bg-primary border border-tertiary mr-2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, computed } from 'vue';
const baseImgUrl = inject('baseImageUrl');

// Helper to capitalize first letter
const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();

// Read and clean the user email from localStorage
const rawUser = (localStorage.getItem('user') || '').replace(/^"+|"+$/g, '');

// Safely split into first & last name
const [firstNameRaw = '', lastNameRaw = ''] = rawUser.split('@')[0]?.split('.') || [];

// Capitalize names for display
const firstName = capitalize(firstNameRaw);
const lastName = capitalize(lastNameRaw);

// Concept info
const conceptId = 2; // Default to Homebox for now, can be dynamic later
const concepts = [
  { name: 'Babyshop', key: 1, img: `${baseImgUrl}/babyshop.png` },
  { name: 'Homebox', key: 2, img: `${baseImgUrl}/homebox.png` },
  { name: 'Lifestyle', key: 3, img: `${baseImgUrl}/lifestyle.png` },
  { name: 'Splash', key: 4, img: `${baseImgUrl}/splash.png` }
];

const conceptObj = computed(() => concepts.find(c => c.key === conceptId) || null);
// const conceptObj = computed(() => concepts.find(c => c.key === conceptId.value) || null);

</script>