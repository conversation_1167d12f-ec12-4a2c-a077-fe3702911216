<template>
  <div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-50 to-green-100">
    <div class="bg-green-200/80 p-8 rounded-2xl shadow-2xl w-[500px] backdrop-blur-md">
      <h2 class="text-center text-black text-2xl font-semibold mb-8 tracking-wide">
        Please Select a Concept
      </h2>

      <!-- Grid for selection -->
      <div class="grid grid-cols-3 gap-4">
        <div
          v-for="concept in concepts"
          :key="concept.value"
          @click="selectedConcept = concept.value"
          class="p-5 rounded-xl border-2 cursor-pointer flex flex-col items-center justify-center transition-all duration-300 ease-out hover:scale-105"
          :class="{
            'bg-green-200 border-green-500 shadow-md': selectedConcept === concept.value,
            'bg-green-700 border-transparent hover:border-green-400 hover:bg-green-600':
              selectedConcept !== concept.value
          }"
        >
          <component
            :is="concept.icon"
            class="w-10 h-10 mb-3"
            :class="selectedConcept === concept.value ? 'text-green-900' : 'text-green-100'"
          />
          <span
            :class="{
              'text-green-900 font-bold': selectedConcept === concept.value,
              'text-green-100 font-medium': selectedConcept !== concept.value
            }"
          >
            {{ concept.label }}
          </span>
        </div>
      </div>

      <!-- Continue button -->
      <button
        class="w-full mt-8 py-3 rounded-xl text-white font-semibold text-lg shadow-lg transition-all duration-300"
        :class="selectedConcept
          ? 'bg-green-600 hover:bg-green-700 active:scale-[0.98]'
          : 'bg-green-800 opacity-50 cursor-not-allowed'"
        :disabled="!selectedConcept"
        @click="handleContinue"
      >
        Continue
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  BuildingOffice2Icon,
  BuildingLibraryIcon,
  BuildingStorefrontIcon
} from "@heroicons/vue/24/outline";

const selectedConcept = ref(null);

const concepts = [
  { value: "concept1", label: "Concept 1", icon: BuildingOffice2Icon },
  { value: "concept2", label: "Concept 2", icon: BuildingLibraryIcon },
  { value: "concept3", label: "Concept 3", icon: BuildingStorefrontIcon },
  { value: "concept4", label: "Concept 4", icon: BuildingLibraryIcon },
  { value: "concept5", label: "Concept 5", icon: BuildingOffice2Icon },
  { value: "concept6", label: "Concept 6", icon: BuildingStorefrontIcon }
];

const handleContinue = () => {
  if (selectedConcept.value) {
    console.log("Selected concept:", selectedConcept.value);
    // API call or router navigation goes here
  }
};
</script>
