<template>
    <div class="p-5 space-y-3">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <label class="flex items-center gap-2 cursor-pointer">
                    <div class="w-4 h-4 bg-red-600 rounded-sm border border-black"></div>
                    <input type="radio" v-model="outlierType" value="MAJOR_OUTLIER" class="sr-only" />
                    <span class="text-sm">Major Outliers</span>
                </label>
                <label class="flex items-center gap-2 cursor-pointer">
                    <div class="w-4 h-4 bg-blue-600 border border-black"></div>
                    <input type="radio" v-model="outlierType" value="MINOR_OUTLIER" class="sr-only" />
                    <span class="text-sm">Minor Outliers</span>
                </label>
            </div>
        </div>

        <!-- Filters -->
        <div class="flex gap-4">
            <div class="w-1/4">
                <label class="text-sm font-semibold">Subclass</label>
                <Multiselect v-model="selectedSubclass" :options="subclassOptions" :searchable="true"
                    placeholder="Select Subclass" label="label" track-by="value" class="mt-1 truncate-multiselect" />
            </div>
            <div class="w-1/4">
                <label class="text-sm font-semibold">Store</label>
                <Multiselect v-model="selectedLocation" :options="locationOptions" :searchable="true"
                    placeholder="Select Store" label="label" track-by="value" class="mt-1" />
            </div>
        </div>

        <!-- Chart -->
        <div class="bg-white rounded-xl shadow p-4 h-[28vw]">
            <canvas id="scatterChart" height="200"></canvas>
        </div>

    </div>
</template>

  <script setup>
  import { ref, watch, onMounted, computed } from 'vue'
  import Chart from 'chart.js/auto'
  import Multiselect from 'vue-multiselect'

  // Props
  const props = defineProps({
    originalRows: {
      type: Array,
      default: () => []
    }
  })
  console.log("props.originalRows", props.originalRows);
  let chartInstance = null

  const selectedSubclass = ref(null)
  const selectedLocation = ref(null)
  const outlierType = ref('')
  const outlierSummary = ref('')

  // Computed options for dropdowns
  const subclassOptions = computed(() => {
    const uniqueSubclasses = [...new Set(props.originalRows.map(d => d.subclass))]
    return uniqueSubclasses.map(d => ({
      label: d,
      value: d
    }))
  })

  const locationOptions = computed(() => {
    const uniqueLocations = [...new Set(props.originalRows.map(d => d.storeId))]
    return uniqueLocations.map(d => ({
      label: d,
      value: d
    }))
  })

  // Filter and redraw chart
  const drawChart = () => {
    if (!props.originalRows.length) return;

    const subclass = selectedSubclass.value?.value || subclassOptions.value[0]?.value;
    const location = selectedLocation.value?.value;
    const type = outlierType.value;

    // Filter data based on selected subclass and location
    let filteredData = props.originalRows.filter(d => d.subclass === subclass);
    if (location) {
      filteredData = filteredData.filter(d => d.storeId === location);
    }

    if (chartInstance) {
      chartInstance.destroy();
    }

    const ctx = document.getElementById('scatterChart');
    chartInstance = new Chart(ctx, {
      type: 'scatter',
      data: {
        datasets: [{
          label: `GMV/Day vs Total Linear Meter for ${subclass}${location ? ` - ${location}` : ''}`,
          data: filteredData.map(d => ({
            x: d.totalLm,
            y: d.gmvPerDay,
            outlierStatus: d.outlierStatus,
            subclass: d.subclass,
            location: d.storeId,
            month: d.month
          })),
          pointBackgroundColor: filteredData.map(d =>
            d.outlierStatus === type
              ? (type === 'MAJOR_OUTLIER' ? 'red' : 'blue')
              : 'gray'
          ),
          pointRadius: filteredData.map(d =>
            d.outlierStatus === type ? 7 : 5
          ),
          pointHoverRadius: 8
        }]
      },
      options: {
        plugins: {
          tooltip: {
            callbacks: {
              label: context => {
                const point = filteredData[context.dataIndex];
                return `Outlier Status: ${point.outlierStatus || 'NORMAL'}`;
              }
            }
          },
          title: {
            display: true,
            text: `GMV/Day vs Total Linear Meter for ${subclass}${location ? ` - ${location}` : ''}`,
            font: { size: 14 }
          }
        },
        scales: {
          x: {
            title: { display: true, text: 'Total Linear Meter' }
          },
          y: {
            title: { display: true, text: 'GMV/Day' }
          }
        }
      }
    });

    // Count only selected outlier type for summary
    const outlierCount = filteredData.filter(d => d.outlierStatus === type).length;
    const outlierTypeLabel = type === 'MAJOR_OUTLIER' ? 'Major' : 'Minor';
    outlierSummary.value = `${outlierCount} ${outlierTypeLabel} Outliers found in ${subclass}${location ? ` - ${location}` : ''}`;
  };

  onMounted(() => {
    if (!selectedSubclass.value && subclassOptions.value.length > 0) {
      selectedSubclass.value = subclassOptions.value[0]
    }
    drawChart()
  })

  watch([selectedSubclass, selectedLocation, outlierType], drawChart)
  watch(() => props.originalRows, () => {
    if (!selectedSubclass.value && subclassOptions.value.length > 0) {
      selectedSubclass.value = subclassOptions.value[0]
    }
    drawChart()
  }, { immediate: true })
  </script>
  
  <style scoped>
  @import "vue-multiselect/dist/vue-multiselect.css";
  
  canvas {
    max-width: 100%;
  }
  .truncate-multiselect ::v-deep .multiselect__single {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 2rem; /* space for dropdown arrow */
  display: block;
  max-width: 100%;
}

  </style>
