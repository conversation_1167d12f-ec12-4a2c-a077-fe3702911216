export interface SpaceHealthRow {
  storeId: string;
  group: string;
  department: string;
  class: string;
  subClass: string;
  sqftExists: 'Yes' | 'No';
  lm: number;
  sqft: number;
  lm2: number;
  sqft2: number;
  optionCount: number;
  optionPerSqft: number;
  sohQty: number;
  sohPerSqft: number;
  revenue: number;
  gmv: number;
  revPerSqft: number;
  gmvPerLm: number;
  cost: number;
  avgStock: number;
  presentRos: number;
  coverDays: number;
  lmCont: number;
  sqftCont: number;
  diff: number;
  children?: SpaceHealthRow[];
}

export const spaceHealthData: SpaceHealthRow[] = [
  {
    storeId: 'S001',
    group: 'Homeware',
    department: '',
    class: '',
    subClass: '-',
    sqftExists: 'Yes',
    lm: 200,
    sqft: 1000,
    lm2: 201,
    sqft2: 1000,
    optionCount: 100,
    optionPerSqft: 0.1,
    sohQty: 400,
    sohPerSqft: 0.4,
    revenue: 50000,
    gmv: 60000,
    revPerSqft: 50,
    gmvPerLm: 300,
    cost: 20000,
    avgStock: 1000,
    presentRos: 1.5,
    coverDays: 30,
    lmCont: 100,
    sqftCont: 50,
    diff: 10,
    children: [
      {
        storeId: 'S001',
        group: 'Homeware',
        department: 'Home Decor',
        class: '',
        subClass: '',
        sqftExists: 'Yes',
        lm: 80,
        sqft: 400,
        lm2: 80,
        sqft2: 400,
        optionCount: 40,
        optionPerSqft: 0.1,
        sohQty: 160,
        sohPerSqft: 0.4,
        revenue: 20000,
        gmv: 25000,
        revPerSqft: 25,
        gmvPerLm: 125,
        cost: 8000,
        avgStock: 400,
        presentRos: 1.2,
        coverDays: 28,
        lmCont: 40,
        sqftCont: 20,
        diff: 4,
        children: [
          {
            storeId: 'S001',
            group: 'Homeware',
            department: 'Home Decor',
            class: 'Decor',
            subClass: '',
            sqftExists: 'Yes',
            lm: 40,
            sqft: 200,
            lm2: 40,
            sqft2: 200,
            optionCount: 20,
            optionPerSqft: 0.1,
            sohQty: 80,
            sohPerSqft: 0.4,
            revenue: 10000,
            gmv: 12000,
            revPerSqft: 12,
            gmvPerLm: 60,
            cost: 4000,
            avgStock: 200,
            presentRos: 1.1,
            coverDays: 25,
            lmCont: 20,
            sqftCont: 10,
            diff: 2,
            children: [
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Decor',
                class: 'Decor',
                subClass: 'Diffusers',
                sqftExists: 'Yes',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 5000,
                gmv: 6000,
                revPerSqft: 6,
                gmvPerLm: 30,
                cost: 2000,
                avgStock: 100,
                presentRos: 1.0,
                coverDays: 20,
                lmCont: 10,
                sqftCont: 5,
                diff: 1,
              },
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Decor',
                class: 'Decor',
                subClass: 'Candles',
                sqftExists: 'No',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 5000,
                gmv: 6000,
                revPerSqft: 6,
                gmvPerLm: 30,
                cost: 2000,
                avgStock: 100,
                presentRos: 1.0,
                coverDays: 20,
                lmCont: 10,
                sqftCont: 5,
                diff: 1,
              },
            ],
          },
          {
            storeId: 'S001',
            group: 'Homeware',
            department: 'Home Decor',
            class: 'Gallery',
            subClass: '-',
            sqftExists: 'No',
            lm: 40,
            sqft: 200,
            lm2: 40,
            sqft2: 200,
            optionCount: 20,
            optionPerSqft: 0.1,
            sohQty: 80,
            sohPerSqft: 0.4,
            revenue: 10000,
            gmv: 12000,
            revPerSqft: 12,
            gmvPerLm: 60,
            cost: 4000,
            avgStock: 200,
            presentRos: 1.0,
            coverDays: 28,
            lmCont: 20,
            sqftCont: 10,
            diff: 2,
            children: [
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Decor',
                class: 'Gallery',
                subClass: 'Clocks',
                sqftExists: 'Yes',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 5000,
                gmv: 6000,
                revPerSqft: 6,
                gmvPerLm: 30,
                cost: 2000,
                avgStock: 100,
                presentRos: 1.0,
                coverDays: 28,
                lmCont: 10,
                sqftCont: 5,
                diff: 1,
              },
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Decor',
                class: 'Gallery',
                subClass: 'Mirrors',
                sqftExists: 'No',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 5000,
                gmv: 6000,
                revPerSqft: 6,
                gmvPerLm: 30,
                cost: 2000,
                avgStock: 100,
                presentRos: 1.0,
                coverDays: 28,
                lmCont: 10,
                sqftCont: 5,
                diff: 1,
              },
            ],
          },
        ],
      },
      {
        storeId: 'S001',
        group: 'Homeware',
        department: 'Home Textiles',
        class: '',
        subClass: '',
        sqftExists: 'No',
        lm: 80,
        sqft: 400,
        lm2: 80,
        sqft2: 400,
        optionCount: 40,
        optionPerSqft: 0.1,
        sohQty: 160,
        sohPerSqft: 0.4,
        revenue: 10000,
        gmv: 12000,
        revPerSqft: 12,
        gmvPerLm: 60,
        cost: 4000,
        avgStock: 200,
        presentRos: 1.0,
        coverDays: 28,
        lmCont: 20,
        sqftCont: 10,
        diff: 2,
        children: [
          {
            storeId: 'S001',
            group: 'Homeware',
            department: 'Home Textiles',
            class: 'Kitchen',
            subClass: '',
            sqftExists: 'Yes',
            lm: 40,
            sqft: 200,
            lm2: 40,
            sqft2: 200,
            optionCount: 20,
            optionPerSqft: 0.1,
            sohQty: 80,
            sohPerSqft: 0.4,
            revenue: 5000,
            gmv: 6000,
            revPerSqft: 6,
            gmvPerLm: 30,
            cost: 2000,
            avgStock: 100,
            presentRos: 1.0,
            coverDays: 28,
            lmCont: 10,
            sqftCont: 5,
            diff: 1,
            children: [
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Textiles',
                class: 'Kitchen',
                subClass: 'Crockery',
                sqftExists: 'Yes',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 2500,
                gmv: 3000,
                revPerSqft: 3,
                gmvPerLm: 15,
                cost: 1000,
                avgStock: 50,
                presentRos: 0.9,
                coverDays: 25,
                lmCont: 5,
                sqftCont: 2.5,
                diff: 0.5,
              },
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Textiles',
                class: 'Kitchen',
                subClass: 'Cutlery',
                sqftExists: 'No',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 2500,
                gmv: 3000,
                revPerSqft: 3,
                gmvPerLm: 15,
                cost: 1000,
                avgStock: 50,
                presentRos: 0.9,
                coverDays: 25,
                lmCont: 5,
                sqftCont: 2.5,
                diff: 0.5,
              },
            ],
          },
          {
            storeId: 'S001',
            group: 'Homeware',
            department: 'Home Textiles',
            class: 'Table',
            subClass: '',
            sqftExists: 'No',
            lm: 40,
            sqft: 200,
            lm2: 40,
            sqft2: 200,
            optionCount: 20,
            optionPerSqft: 0.1,
            sohQty: 80,
            sohPerSqft: 0.4,
            revenue: 5000,
            gmv: 6000,
            revPerSqft: 6,
            gmvPerLm: 30,
            cost: 2000,
            avgStock: 100,
            presentRos: 1.0,
            coverDays: 28,
            lmCont: 10,
            sqftCont: 5,
            diff: 1,
            children: [
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Textiles',
                class: 'Table',
                subClass: 'Napkins',
                sqftExists: 'Yes',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 2500,
                gmv: 3000,
                revPerSqft: 3,
                gmvPerLm: 15,
                cost: 1000,
                avgStock: 50,
                presentRos: 0.9,
                coverDays: 25,
                lmCont: 5,
                sqftCont: 2.5,
                diff: 0.5,
              },
              {
                storeId: 'S001',
                group: 'Homeware',
                department: 'Home Textiles',
                class: 'Table',
                subClass: 'Spoons',
                sqftExists: 'No',
                lm: 20,
                sqft: 100,
                lm2: 20,
                sqft2: 100,
                optionCount: 10,
                optionPerSqft: 0.1,
                sohQty: 40,
                sohPerSqft: 0.4,
                revenue: 2500,
                gmv: 3000,
                revPerSqft: 3,
                gmvPerLm: 15,
                cost: 1000,
                avgStock: 50,
                presentRos: 0.9,
                coverDays: 25,
                lmCont: 5,
                sqftCont: 2.5,
                diff: 0.5,
              },
            ],
          },
        ],
      },
    ],
  },
  // Add two more groups (e.g., Home, Electronics) with similar nested structure for demo
]; 