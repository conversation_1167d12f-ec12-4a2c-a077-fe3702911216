<template>
  <div class="flex w-full">
    <div class="flex-1 flex flex-col">
      <main class="flex-1 p-4 sm:p-6 lg:p-8 ">
        <div class="space-y-10">
          <!-- Table 1 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-bold mb-4">Store Level (in Scope Depts) WTD</h2>
            <EvaluationTable :tableData="table1Data" />
          </div>
          <!-- Table 2 -->
          <!-- Table 3 -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-bold mb-4">RTL Quantity, Revenue, GMV, Invoice</h2>
            <EvaluationCollapsibleTable :tableData="table3Data" />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import EvaluationTable from './EvaluationTable.vue';
import EvaluationCollapsibleTable from './EvaluationCollapsibleTable.vue';

// Updated mock data for Table 1 and 2
const table1Data = [
  {
    label: 'Test Store',
    wtd: {
      revenue: { home: '10%', nonHome: '8%', overall: '9%' },
      gmv: { home: '12%', nonHome: '10%', overall: '11%' },
    },
    prior: {
      revenue: { home: '5%', nonHome: '3%', overall: '4%' },
      gmv: { home: '6%', nonHome: '4%', overall: '5%' },
    },
  },
  {
    label: 'Control store 1',
    wtd: {
      revenue: { home: '-5%', nonHome: '-7%', overall: '-6%' },
      gmv: { home: '-4%', nonHome: '-8%', overall: '-6%' },
    },
    prior: {
      revenue: { home: '-2%', nonHome: '-3%', overall: '-2%' },
      gmv: { home: '-1%', nonHome: '-4%', overall: '-2%' },
    },
  },
  {
    label: 'Control store 2',
    wtd: {
      revenue: { home: '0%', nonHome: '-2%', overall: '-1%' },
      gmv: { home: '1%', nonHome: '-3%', overall: '-1%' },
    },
    prior: {
      revenue: { home: '2%', nonHome: '0%', overall: '1%' },
      gmv: { home: '3%', nonHome: '1%', overall: '2%' },
    },
  },
  {
    label: 'Territory UAE',
    wtd: {
      revenue: { home: '7%', nonHome: '6%', overall: '7%' },
      gmv: { home: '8%', nonHome: '7%', overall: '8%' },
    },
    prior: {
      revenue: { home: '4%', nonHome: '3%', overall: '4%' },
      gmv: { home: '5%', nonHome: '4%', overall: '5%' },
    },
  },
];
const table2Data = table1Data; // For demo, reuse table1Data

// Mock data for Table 3 (unchanged)
const table3Data = [
  {
    store: 'Dalma',
    grp_pm: 'Fashion',
    dpt_nm: 'Bags',
    rtl_qty: { wtd: 473, lywtd: 549, growth: '-14%' },
    revenue: { wtd: 473, lywtd: 549, growth: '-14%' },
    gmv: { wtd: 473, lywtd: 549, growth: '-14%' },
    invoice: { wtd: 473, lywtd: 549, growth: '-14%' },
  },
  {
    store: 'Dalma',
    grp_pm: 'Home',
    dpt_nm: 'Bathroom',
    rtl_qty: { wtd: 4, lywtd: 4, growth: '0%' },
    revenue: { wtd: 4, lywtd: 4, growth: '0%' },
    gmv: { wtd: 4, lywtd: 4, growth: '0%' },
    invoice: { wtd: 4, lywtd: 4, growth: '0%' },
  },
];
</script> 