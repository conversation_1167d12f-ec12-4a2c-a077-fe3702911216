import { defineStore } from 'pinia'

export const useScenarioStore = defineStore('scenario', {
  state: () => ({
    currentScenario: null,
    scenarioHistory: [],
    isLoading: false,
    error: null
  }),

  getters: {
    // Get current scenario details
    getCurrentScenario: (state) => state.currentScenario,
    
    // Get scenario ID
    getScenarioId: (state) => state.currentScenario?.scenario_id || null,
    
    // Get scenario name
    getScenarioName: (state) => state.currentScenario?.name || '',
    
    // Get scenario configuration
    getScenarioConfig: (state) => {
      if (!state.currentScenario) return null
      return {
        eval_type: state.currentScenario.eval_type,
        season_type: state.currentScenario.season_type,
        metric: state.currentScenario.metric,
        concept_name: state.currentScenario.concept_name,
        territory_name: state.currentScenario.territory_name
      }
    },
    
    // Get scenario dates
    getScenarioDates: (state) => {
      if (!state.currentScenario) return null
      return {
        eval_start: state.currentScenario.eval_start,
        eval_end: state.currentScenario.eval_end,
        ref_start: state.currentScenario.ref_start,
        ref_end: state.currentScenario.ref_end
      }
    },
    
    // Get location codes
    getLocationCodes: (state) => {
      if (!state.currentScenario?.location_codes) return []
      try {
        return JSON.parse(state.currentScenario.location_codes)
      } catch (error) {
        console.error('Error parsing location codes:', error)
        return []
      }
    },
    
    // Check if scenario is loaded
    hasScenario: (state) => !!state.currentScenario,
    
    // Get loading state
    isScenarioLoading: (state) => state.isLoading,
    
    // Get error state
    getScenarioError: (state) => state.error
  },

  actions: {
    // Set current scenario from API response
    setCurrentScenario(scenarioDetails) {
      console.log('Setting current scenario:', scenarioDetails)
      this.currentScenario = { ...scenarioDetails }
      this.error = null
      
      // Add to history if not already present
      const existingIndex = this.scenarioHistory.findIndex(
        scenario => scenario.scenario_id === scenarioDetails.scenario_id
      )
      
      if (existingIndex === -1) {
        this.scenarioHistory.push({ ...scenarioDetails })
      } else {
        // Update existing scenario in history
        this.scenarioHistory[existingIndex] = { ...scenarioDetails }
      }
    },
    
    // Update specific scenario properties
    updateScenarioProperty(property, value) {
      if (this.currentScenario) {
        this.currentScenario[property] = value
      }
    },
    
    // Clear current scenario
    clearCurrentScenario() {
      this.currentScenario = null
      this.error = null
    },
    
    // Set loading state
    setLoading(loading) {
      this.isLoading = loading
    },
    
    // Set error state
    setError(error) {
      this.error = error
      this.isLoading = false
    },
    
    // Get scenario from history by ID
    getScenarioFromHistory(scenarioId) {
      return this.scenarioHistory.find(
        scenario => scenario.scenario_id === scenarioId
      )
    },
    
    // Load scenario by ID (if exists in history)
    loadScenarioById(scenarioId) {
      const scenario = this.getScenarioFromHistory(scenarioId)
      if (scenario) {
        this.setCurrentScenario(scenario)
        return true
      }
      return false
    },
    
    // Clear all scenario data
    clearAllScenarios() {
      this.currentScenario = null
      this.scenarioHistory = []
      this.error = null
      this.isLoading = false
    },
    
    // Export scenario data for API calls
    exportScenarioForAPI() {
      if (!this.currentScenario) return null
      
      return {
        scenario_id: this.currentScenario.scenario_id,
        name: this.currentScenario.name,
        concept: this.currentScenario.concept_name,
        territory: this.currentScenario.territory_name,
        eval_type: this.currentScenario.eval_type,
        season_type: this.currentScenario.season_type,
        metric: this.currentScenario.metric,
        location_codes: this.getLocationCodes,
        eval_start: this.currentScenario.eval_start,
        eval_end: this.currentScenario.eval_end,
        ref_start: this.currentScenario.ref_start,
        ref_end: this.currentScenario.ref_end
      }
    }
  }
})
