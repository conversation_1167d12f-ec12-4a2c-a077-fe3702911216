<template>
    <div class="p-3">
      <div class="flex flex-wrap gap-4 items-end bg-white p-6 rounded-xl shadow-md">
        <div
          v-for="(filter, key) in filterOptions"
          :key="key"
          class="flex flex-col w-48"
        >
          <label class="text-sm font-semibold text-gray-600">{{ filter.label }}</label>
          <multiselect
            v-model="filters[key]"
            :options="filter.options"
            :multiple="true"
            :close-on-select="false"
            :placeholder="filter.placeholder"
            class="multiselect"
          />
        </div>
        <button
          @click="applyFilters"
          class="flex h-10 px-6 bg-blue-600 text-white rounded-md items-center font-semibold hover:bg-blue-700 transition"
        >
          Filter
        </button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import Multiselect from 'vue-multiselect'
  import 'vue-multiselect/dist/vue-multiselect.min.css'
  
  const filters = ref({
    storeId: [],
    storeName: [],
    fixtureKey: [],
    zone: [],
    item: []
  })
  
  const filterOptions = {
    storeId: {
      label: 'Store ID',
      options: ['001', '002', '003'],
      placeholder: 'Select Store IDs'
    },
    storeName: {
      label: 'Store Name',
      options: ['Store A', 'Store B', 'Store C'],
      placeholder: 'Select Store Names'
    },
    fixtureKey: {
      label: 'Fixture Key',
      options: ['FX-01', 'FX-02', 'FX-03'],
      placeholder: 'Select Fixture Keys'
    },
    zone: {
      label: 'Zone',
      options: ['Zone 1', 'Zone 2', 'Zone 3'],
      placeholder: 'Select Zones'
    },
    item: {
      label: 'Item',
      options: ['Item X', 'Item Y', 'Item Z'],
      placeholder: 'Select Items'
    }
  }
  
  function applyFilters() {
    console.log('Applied Filters:', filters.value)
    // Emit or handle filter logic externally if needed
  }
  </script>
  
  <style scoped>
  .multiselect {
    min-height: 40px;
  }
  </style>
  