<script setup lang="ts">
import FilterBarTest from './FilterBarTest.vue';
import SpaceHealthTable from './SpaceHealthTable.vue';
import SpaceHealthMetricsTable from './SpaceHealthMetricsTable.vue';
</script>

<template>
  <div class="flex flex-col h-full ">
    <!-- Fixed Filter Bar at top -->
    <div class="flex-shrink-0 bg-primary">
      <FilterBarTest />
    </div>
    
    <!-- Scrollable Table Area -->
    <div class="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
      <div class="rounded shadow">
        <div class="p-4">
          <!-- <SpaceHealthTable /> -->
           <SpaceHealthMetricsTable />
        </div>
      </div>
    </div>
  </div>
</template>