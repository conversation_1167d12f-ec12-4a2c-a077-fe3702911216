<!-- <script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from './components/Sidebar.vue'
import conceptBar from './components/ConceptBar.vue'

const isExpanded = ref(false)
const route = useRoute();
const shouldShowSidebar = computed(() => route.path !== '/' && route.path !== '/concept-selection');
const shouldShowHomePage = computed(() => route.path !== '/' && route.path !== '/callback');

const expandSidebar = () => {
  isExpanded.value = true;
};

const collapseSidebar = () => {
  isExpanded.value = false;
};
</script>

<template>
  <div class="flex bg-gray-50 relative font-sans">
    <div class="flex fixed top-0 left-0 h-full transition-all duration-300"  :class="isExpanded ? 'w-[19vw]' : 'w-[5vw]'">
      <Sidebar
      v-if="shouldShowSidebar"
      :isExpanded="isExpanded"
      @mouseover="expandSidebar"
      @mouseleave="collapseSidebar"
    />
  </div>
    <div class="flex flex-col ml-18 transition-all duration-300" :class="isExpanded ? 'ml-[19vw]' : 'ml-[5vw]'">
      <conceptBar class="flex" v-if="shouldShowHomePage" />
      <main class="flex">
        <router-view />
      </main>
    </div>
  </div>
</template> -->


<script>
import { defineComponent } from 'vue'
import { RouterView } from 'vue-router' 

export default defineComponent({
  name: 'App',
  components: {
    RouterView,
  },
})
</script>

<template>
  <RouterView />
</template>