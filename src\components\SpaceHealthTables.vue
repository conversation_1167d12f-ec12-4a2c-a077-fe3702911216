<template>
    <div class="p-4 overflow-x-auto">
      <table class="min-w-full text-sm border border-gray-300 text-left bg-white">
        <thead class="bg-gray-100">
          <tr>
            <th class="px-3 py-2 border">Sub Class</th>
            <th class="px-3 py-2 border">Sqft Exists</th>
            <th class="px-3 py-2 border">LM</th>
            <th class="px-3 py-2 border">Sqft</th>
            <th class="px-3 py-2 border">LM/Sqft</th>
            <th class="px-3 py-2 border">Options</th>
            <th class="px-3 py-2 border">Options/LM</th>
            <th class="px-3 py-2 border">SOH/LM</th>
            <th class="px-3 py-2 border">Revenue</th>
            <th class="px-3 py-2 border">GMV</th>
            <th class="px-3 py-2 border">Revenue/SPD</th>
            <th class="px-3 py-2 border">GMV/Day</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, index) in tableData"
            :key="index"
            :class="{
              'bg-green-100': row.highlight,
              'text-red-600': row.SqftExists === 'No'
            }"
          >
            <td class="px-3 py-2 border">{{ row.subClass }}</td>
            <td class="px-3 py-2 border">{{ row.SqftExists }}</td>
            <td class="px-3 py-2 border text-center">{{ row.LM }}</td>
            <td class="px-3 py-2 border text-center">{{ row.Sqft }}</td>
            <td class="px-3 py-2 border text-center">{{ row.lmPerSqft }}</td>
            <td class="px-3 py-2 border text-center">{{ row.optionCount }}</td>
            <td class="px-3 py-2 border text-center">{{ row.optionPerLm }}</td>
            <td class="px-3 py-2 border text-center">{{ row.sohPerLm }}</td>
            <td class="px-3 py-2 border text-right">{{ row.revenue.toLocaleString() }}</td>
            <td class="px-3 py-2 border text-right">{{ row.gmv.toLocaleString() }}</td>
            <td class="px-3 py-2 border text-center">{{ row.revenuePerSpd }}</td>
            <td class="px-3 py-2 border text-center">{{ row.gmvPerDay }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  
  const tableData = ref([
    {
      subClass: 'ACCENTS AND STORAGE',
      SqftExists: 'Yes',
      LM: 28,
      Sqft: 136,
      lmPerSqft: 0.2,
      optionCount: 183,
      optionPerLm: 6.58,
      sohPerLm: 21.26,
      revenue: 21742,
      gmv: 12637,
      revenuePerSpd: 236,
      gmvPerDay: 137,
    },
    {
      subClass: 'DIFFUSERS & BURNERS',
      SqftExists: 'No',
      LM: 21,
      Sqft: 36,
      lmPerSqft: '',
      optionCount: 220,
      optionPerLm: 10.6,
      sohPerLm: 44.23,
      revenue: 18820,
      gmv: 9206,
      revenuePerSpd: 205,
      gmvPerDay: 105,
    },
    {
      subClass: 'PHOTO FRAMES',
      SqftExists: 'Yes',
      LM: 65,
      Sqft: 581,
      lmPerSqft: 0.11,
      optionCount: 530,
      optionPerLm: 8.13,
      sohPerLm: 19.71,
      revenue: 60108,
      gmv: 32815,
      revenuePerSpd: 653,
      gmvPerDay: 357,
      highlight: true,
    },
    {
      subClass: 'ACCENTS AND STORAGE',
      SqftExists: 'Yes',
      LM: 28,
      Sqft: 136,
      lmPerSqft: 0.2,
      optionCount: 183,
      optionPerLm: 6.58,
      sohPerLm: 21.26,
      revenue: 21742,
      gmv: 12637,
      revenuePerSpd: 236,
      gmvPerDay: 137,
    },
    {
      subClass: 'DIFFUSERS & BURNERS',
      SqftExists: 'No',
      LM: 21,
      Sqft: 36,
      lmPerSqft: '',
      optionCount: 220,
      optionPerLm: 10.6,
      sohPerLm: 44.23,
      revenue: 18820,
      gmv: 9206,
      revenuePerSpd: 205,
      gmvPerDay: 105,
    },
    {
      subClass: 'PHOTO FRAMES',
      SqftExists: 'Yes',
      LM: 65,
      Sqft: 581,
      lmPerSqft: 0.11,
      optionCount: 530,
      optionPerLm: 8.13,
      sohPerLm: 19.71,
      revenue: 60108,
      gmv: 32815,
      revenuePerSpd: 653,
      gmvPerDay: 357,
      highlight: true,
    },
    {
      subClass: 'ACCENTS AND STORAGE',
      SqftExists: 'Yes',
      LM: 28,
      Sqft: 136,
      lmPerSqft: 0.2,
      optionCount: 183,
      optionPerLm: 6.58,
      sohPerLm: 21.26,
      revenue: 21742,
      gmv: 12637,
      revenuePerSpd: 236,
      gmvPerDay: 137,
    },
    {
      subClass: 'DIFFUSERS & BURNERS',
      SqftExists: 'No',
      LM: 21,
      Sqft: 36,
      lmPerSqft: '',
      optionCount: 220,
      optionPerLm: 10.6,
      sohPerLm: 44.23,
      revenue: 18820,
      gmv: 9206,
      revenuePerSpd: 205,
      gmvPerDay: 105,
    },
    {
      subClass: 'PHOTO FRAMES',
      SqftExists: 'Yes',
      LM: 65,
      Sqft: 581,
      lmPerSqft: 0.11,
      optionCount: 530,
      optionPerLm: 8.13,
      sohPerLm: 19.71,
      revenue: 60108,
      gmv: 32815,
      revenuePerSpd: 653,
      gmvPerDay: 357,
      highlight: true,
    },
  ])
  </script>
  