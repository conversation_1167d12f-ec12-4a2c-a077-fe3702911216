<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import axios from 'axios'
import dayjs from 'dayjs'
import { API_BASE_PATH } from '../constant.js'
import DynamicFilter from './common/DynamicFilter.vue'
import * as echarts from 'echarts'
import { useStepsStore } from '../stores/NavigationStore'
import { useScenarioStore } from '../stores/ScenarioStore.js'

const scenarioStore = useScenarioStore()
const stepsStore = useStepsStore()
const selectedStores = ref('')
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubClasses = ref([])
const fromMonth = ref('')
const toMonth = ref('')
const storeOnSimilarity = ref([])

const scenarioDetails = {
  concept: 'hb',
  scenario_id: ref(null),
  from_month: '2024-01',
  to_month: '2024-12',
  selectedMetric: ref('gmv'),
  loc_cd: '',
  dataSummary: ref([]),
  gdcsData: ref([]),
  metricTop5: ref([]),
  metricBottom5: ref([])
}

onMounted(async () => {
  const storedData = stepsStore.getScenarioData
  if (storedData && storedData.scenario_id) {
    scenarioDetails.scenario_id.value = storedData.scenario_id
  }
  const storeConfig = scenarioStore.getScenarioConfig.eval_type;
  let locCodes = []
  if(storeConfig == "Test & Control"){
    locCodes = sessionStorage.getItem('loc_codes')
  }else{
    locCodes = sessionStorage.getItem('loc_codes')
  }
  locCodes = JSON.parse(locCodes);
  // if (storedData) {

    // Handle Test & Control
    // if (storedData.configurationStore === 'Test & Control') {
    //   try {
    //     storedData.storeSelection.forEach(item => {
    //       if (item.testStore) locCodes.push(item.testStore)
    //       if (Array.isArray(item.controlStores)) {
    //         locCodes.push(...item.controlStores)
    //       }
    //     })
    //   } catch (err) {
    //     console.error('Error parsing loc_cd JSON (Test & Control):', err)
    //   }
    // }

    // // Handle Selected Stores
    // if (storedData.configurationStore === 'Selected Stores' && storedData.storeSelection) {
    //   try {
    //     storedData.storeSelection.forEach(item => {
    //       if (item.testStore) locCodes.push(item.testStore)
    //     })
    //   } catch (err) {
    //     console.error('Error parsing loc_cd JSON (Selected Stores):', err)
    //   }
    // }

    // Assign first location code if available
    if (locCodes.length) {
      scenarioDetails.loc_cd = locCodes
      console.log('Selected store code:', scenarioDetails.loc_cd)
    }
  // }
  if (!fromMonth.value) {
    fromMonth.value = dayjs().subtract(6, 'month').format('YYYY-MM')
  }
  if (!toMonth.value) {
    toMonth.value = dayjs().format('YYYY-MM')
  }
  await getGdcsData()
  await getDataSummary()
  await getMetricGraph()
})

const getDataSummary = async () => {
  try {
    const response = await axios.post(`${API_BASE_PATH}/scenario/getDataSummary/`, {
      concept: scenarioDetails.concept,
      // scenario_id: scenarioDetails.scenario_id.value,
      scenario_id : scenarioStore.getScenarioId,
      group: selectedGroups.value,
      department: selectedDepartments.value,
      class_field: selectedClasses.value,
      sub_class: selectedSubClasses.value,
      from_month: fromMonth.value,
      to_month: toMonth.value,
      loc_cd: selectedStores.value ? String(selectedStores.value) : ''
    })
    scenarioDetails.dataSummary.value = response.data.data_points
  } catch (err) {
    console.error('Error fetching Data:', err)
  }
}

const getGdcsData = async () => {
  try {
    const response = await axios.post(`${API_BASE_PATH}/scenario/getAllGDCSdata/`, {
      concept: scenarioDetails.concept,
      // scenario_id: scenarioDetails.scenario_id.value
      scenario_id : scenarioStore.getScenarioId,
    })
    scenarioDetails.gdcsData.value = response.data.gdcs_data
    // Assign zeroth store code to selectedStores if available
    // if (scenarioDetails.gdcsData.value.length > 0) {
    //   selectedStores.value = scenarioDetails.gdcsData.value[0].LOC_CD
    // }
  } catch (err) {
    console.error('Error fetching Data:', err)
  }
}
const getMetricGraph = async () => {
  try {
    console.log('Fetching metric graph data for:', scenarioDetails)
    const response = await axios.post(`${API_BASE_PATH}/scenario/getMetricGraph/`, {
      concept: scenarioDetails.concept,
      // scenario_id: scenarioDetails.scenario_id.value,
      scenario_id : scenarioStore.getScenarioId,
      metric: scenarioDetails.selectedMetric.value,
      // loc_cd: selectedStores.value || scenarioDetails.loc_cd,
      loc_cd: scenarioDetails.loc_cd,
    })
    scenarioDetails.metricTop5.value = response.data.top_5
    scenarioDetails.metricBottom5.value = response.data.bottom_5
  } catch (err) {
    console.error('Error fetching Data:', err)
  }
}
// Chart reference
const chartContainer = ref(null)
let chartInstance = null

// Computed properties for unique filter options
const uniqueStores = computed(() => {
  const stores = scenarioDetails.gdcsData.value.map(item => ({
    value: item.LOC_CD,
    label: `${item.LOC_NM} (${item.LOC_CD})`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }

  const groups = [...new Set(filtered.map(item => item.GRP_NM))]
  return groups.map(group => ({ value: group, label: group }))
})

const uniqueDepartments = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }

  const departments = [...new Set(filtered.map(item => item.DPT_NM))]
  return departments.map(dept => ({ value: dept, label: dept }))
})

const uniqueClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }

  const classes = [...new Set(filtered.map(item => item.CLSS_NM))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value) {
    filtered = filtered.filter(item => item.LOC_CD === selectedStores.value)
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }
  if (selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.CLSS_NM))
  }

  const subClasses = [...new Set(filtered.map(item => item.SUB_CLSS_NM))]
  return subClasses.map(subCls => ({ value: subCls, label: subCls }))
})


// Filtered data based on selections
const filteredData = computed(() => {
  let data = scenarioDetails.dataSummary.value

  // Filter by date range
  if (fromMonth.value) {
    data = data.filter(item => item.month >= fromMonth.value.replace('-', ''))
  }
  if (toMonth.value) {
    data = data.filter(item => item.month <= toMonth.value.replace('-', ''))
  }

  // Filter by group selection
  if (selectedGroups.value.length > 0) {
    data = data.filter(item => selectedGroups.value.includes(item.group_name))
  }

  return data.sort((a, b) => a.month.localeCompare(b.month))
})

// Chart data preparation
const chartData = computed(() => {
  const data = filteredData.value
  const months = data.map(item => {
    const year = item.month.substring(0, 4)
    const month = item.month.substring(4, 6)
    return `${year}-${month}`
  })

  const linearMeters = data.map(item => Math.round(item.total_lm * 100) / 100)
  const productivity = data.map(item => Math.round(item.productivity * 100) / 100)

  return {
    months,
    linearMeters,
    productivity
  }
})

// Chart methods
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance || !chartData.value.months.length) return

  const option = {
    title: {
      text: 'Linear Meters vs Productivity',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['Linear Meters', 'Productivity'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.months,
      axisLabel: {
        formatter: function (value) {
          const [year, month] = value.split('-')
          return `${month}/${year.substring(2)}`
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: 'Linear Meters',
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: 'Productivity',
        position: 'right',
        axisLabel: {
          formatter: '{value}%'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: 'Linear Meters',
        type: 'line',
        yAxisIndex: 0,
        data: chartData.value.linearMeters,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#3B82F6',
          width: 3
        },
        itemStyle: {
          color: '#3B82F6'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(59, 130, 246, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(59, 130, 246, 0.05)'
            }
          ])
        }
      },
      {
        name: 'Productivity',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.value.productivity,
        smooth: true,
        symbol: 'diamond',
        symbolSize: 6,
        lineStyle: {
          color: '#EF4444',
          width: 3
        },
        itemStyle: {
          color: '#EF4444'
        }
      }
    ]
  }

  chartInstance.setOption(option, true)
}

// Event handlers
const handleDateChange = () => {
  nextTick(() => {
    updateChart()
  })
}

const clearAllFilters = () => {
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  fromMonth.value = ''
  toMonth.value = ''
}


// Watchers
watch(filteredData, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch([selectedGroups, selectedStores, selectedDepartments, selectedClasses, selectedSubClasses], () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initChart()

    // Handle window resize
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    window.addEventListener('resize', handleResize)

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance) {
        chartInstance.dispose()
      }
    }
  })
})


// Chart references
const topChartContainer = ref(null)
const bottomChartContainer = ref(null)
let topChartInstance = null
let bottomChartInstance = null


// Metric options mapping
const metricMapping = {
  'units_per_inv': 'Units Per Invoice',
  'cust_pen': 'Customer Penetration',
  'cover': 'Cover',
  'margin_perc': 'Margin',
  'gmv': 'Productivity',
  'asp': 'Average Selling Price'
}

const metricOptions = computed(() => {
  return Object.entries(metricMapping).map(([key, label]) => ({
    value: key,
    label: label
  }))
})

const selectedMetricLabel = computed(() => {
  return metricMapping[scenarioDetails.selectedMetric.value] || 'Productivity'
})

// Computed properties for chart data
const topMetricsData = computed(() => {
  const data = scenarioDetails?.metricTop5?.value
  return data.map(item => ({
    name: item.sub_class_name,
    value: Math.round(item.metric_value * 100) / 100,
    rank: item.rank
  })).sort((a, b) => b.value - a.value) // Sort by value descending
})

const bottomMetricsData = computed(() => {
  const data = scenarioDetails?.metricBottom5?.value
  return data.map(item => ({
    name: item.sub_class_name,
    value: Math.round(item.metric_value * 100) / 100,
    rank: item.rank
  })).sort((a, b) => a.value - b.value) // Sort by value ascending
})

// Summary statistics
const topPerformerValue = computed(() => {
  return topMetricsData.value.length > 0 ? topMetricsData.value[0].value : 0
})

const topPerformerName = computed(() => {
  return topMetricsData.value.length > 0 ? topMetricsData.value[0].name : 'N/A'
})

const bottomPerformerValue = computed(() => {
  return bottomMetricsData.value.length > 0 ? bottomMetricsData.value[0].value : 0
})

const bottomPerformerName = computed(() => {
  return bottomMetricsData.value.length > 0 ? bottomMetricsData.value[0].name : 'N/A'
})

const averageTop5 = computed(() => {
  if (topMetricsData.value.length === 0) return 0
  const avg = topMetricsData.value.reduce((sum, item) => sum + item.value, 0) / topMetricsData.value.length
  return Math.round(avg * 100) / 100
})

const averageBottom5 = computed(() => {
  if (bottomMetricsData.value.length === 0) return 0
  const avg = bottomMetricsData.value.reduce((sum, item) => sum + item.value, 0) / bottomMetricsData.value.length
  return Math.round(avg * 100) / 100
})

// Chart initialization and update methods
const initTopChart = () => {
  if (!topChartContainer.value) return
  topChartInstance = echarts.init(topChartContainer.value)
  updateTopChart()
}

const initBottomChart = () => {
  if (!bottomChartContainer.value) return
  bottomChartInstance = echarts.init(bottomChartContainer.value)
  updateBottomChart()
}

const updateTopChart = () => {
  if (!topChartInstance || topMetricsData.value.length === 0) return

  const option = {
    title: {
      text: 'Top 5 Performers',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#065f46'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const param = params[0]
        return `<strong>${param.name}</strong><br/>
                ${selectedMetricLabel.value}: ${param.value}<br/>
                Rank: #${param.data.rank || (param.dataIndex + 1)}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: topMetricsData.value.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        formatter: function (value) {
          return value.length > 15 ? value.substring(0, 15) + '...' : value
        }
      }
    },
    yAxis: {
      type: 'value',
      name: selectedMetricLabel.value,
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [{
      type: 'bar',
      data: topMetricsData.value.map((item, index) => ({
        value: item.value,
        rank: item.rank,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#22c55e' },
            { offset: 1, color: '#15803d' }
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10,
        color: '#065f46',
        fontWeight: 'bold'
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#22c55e' },
            { offset: 1, color: '#15803d' }
          ])
        }
      }
    }]
  }

  topChartInstance.setOption(option, true)
}

const updateBottomChart = () => {
  if (!bottomChartInstance || bottomMetricsData.value.length === 0) return

  const option = {
    title: {
      text: 'Bottom 5 Performers',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#dc2626'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const param = params[0]
        return `<strong>${param.name}</strong><br/>
                ${selectedMetricLabel.value}: ${param.value}<br/>
                Rank: #${param.data.rank || (param.dataIndex + 1)}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: bottomMetricsData.value.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        formatter: function (value) {
          return value.length > 15 ? value.substring(0, 15) + '...' : value
        }
      }
    },
    yAxis: {
      type: 'value',
      name: selectedMetricLabel.value,
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [{
      type: 'bar',
      data: bottomMetricsData.value.map((item, index) => ({
        value: item.value,
        rank: item.rank,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ef4444' }, // lighter red (red-500)
    { offset: 1, color: '#991b1b' }  // darker red (red-800)
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10,
        color: '#dc2626',
        fontWeight: 'bold'
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ef4444' }, // lighter red (red-500)
    { offset: 1, color: '#991b1b' }  // darker red (red-800)
          ])
        }
      }
    }]
  }

  bottomChartInstance.setOption(option, true)
}

// Event handlers
const handleMetricChange = () => {
  // Here you would typically make an API call to fetch new data based on selected metric
  // For now, we'll just update the charts with current data
  nextTick(() => {
    updateTopChart()
    updateBottomChart()
  })
}

// Watchers
watch([topMetricsData, bottomMetricsData], () => {
  nextTick(() => {
    updateTopChart()
    updateBottomChart()
  })
}, { deep: true })

watch(scenarioDetails.selectedMetric, () => {
  // Here you would make API call with new metric
  getMetricGraph()
})

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initTopChart()
    initBottomChart()

    // Handle window resize
    const handleResize = () => {
      if (topChartInstance) topChartInstance.resize()
      if (bottomChartInstance) bottomChartInstance.resize()
    }

    window.addEventListener('resize', handleResize)

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize)
      if (topChartInstance) {
        topChartInstance.dispose()
        topChartInstance = null
      }
      if (bottomChartInstance) {
        bottomChartInstance.dispose()
        bottomChartInstance = null
      }
    }
  })
})

</script>

<template>
  <div class="p-6 bg-gray-50">
    <div class="max-w-7xl mx-auto">
      <!-- Filters Section -->
      <div class="bg-white rounded-lg shadow-sm p-2 mb-2">

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Select Store</label>
            <DynamicFilter v-model="selectedStores" :multiselect="false" label="Store" placeholder="Select Stores"
              :options="uniqueStores" variant="secondary" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Select Groups</label>
            <DynamicFilter v-model="selectedGroups" :multiselect="true" label="Groups" placeholder="Select Groups"
              :options="uniqueGroups" variant="secondary" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Select Departments</label>
            <DynamicFilter v-model="selectedDepartments" :multiselect="true" label="Departments"
              placeholder="Select Departments" :options="uniqueDepartments" variant="secondary" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Select Classes</label>
            <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Classes" placeholder="Select Classes"
              :options="uniqueClasses" variant="secondary" size="sm" />
          </div>
        </div>

        <!-- Second Row of Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Select Sub Classes</label>
            <DynamicFilter v-model="selectedSubClasses" :multiselect="true" label="Sub Classes"
              placeholder="Select Sub Classes" :options="uniqueSubClasses" variant="secondary" size="sm" />
          </div>

          <div class="flex flex-col">
            <label class="block text-sm font-medium text-gray-700 mb-1">From Month</label>
            <input v-model="fromMonth" type="month"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              @change="handleDateChange" />
          </div>

          <div class="flex flex-col">
            <label class="block text-sm font-medium text-gray-700 mb-1">To Month</label>
            <input v-model="toMonth" type="month"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              @change="handleDateChange" />
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex justify-end mt-4 gap-2">
          <button @click="clearAllFilters"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
            Clear All
          </button>
          <button @click="getDataSummary()"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            Apply Filters
          </button>
        </div>
      </div>

      <!-- Chart Section -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-800">Performance Trends</h2>
          <div class="text-sm text-gray-500">
            Showing data for {{ filteredData.length }} records
          </div>
        </div>

        <div ref="chartContainer" class="w-full h-96"></div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-sm p-6 mt-3">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800">Performance Metrics Comparison</h2>
        <div class="w-64">
          <DynamicFilter v-model="scenarioDetails.selectedMetric.value" :multiselect="false" label="Metric"
            placeholder="Select Metric" :options="metricOptions" variant="secondary" size="sm"
            @change="handleMetricChange" />
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top 5 Chart -->
        <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-green-800 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                  clip-rule="evenodd" />
              </svg>
              Top 5 Performers
            </h3>
            <span class="text-sm text-green-600 font-medium">{{ selectedMetricLabel }}</span>
          </div>
          <div ref="topChartContainer" class="w-full h-80"></div>
        </div>

        <!-- Bottom 5 Chart -->
        <div class="bg-gradient-to-br from-red-50 to-rose-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-red-800 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"
                  clip-rule="evenodd" />
              </svg>
              Bottom 5 Performers
            </h3>
            <span class="text-sm text-red-600 font-medium">{{ selectedMetricLabel }}</span>
          </div>
          <div ref="bottomChartContainer" class="w-full h-80"></div>
        </div>
      </div>

      <!-- Summary Stats -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-600">{{ topPerformerValue }}</div>
          <div class="text-sm text-gray-600">Best Performer</div>
          <div class="text-xs text-gray-500 mt-1 truncate">{{ topPerformerName }}</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-green-500">{{ averageTop5 }}</div>
          <div class="text-sm text-gray-600">Top 5 Average</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-500">{{ averageBottom5 }}</div>
          <div class="text-sm text-gray-600">Bottom 5 Average</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-600">{{ bottomPerformerValue }}</div>
          <div class="text-sm text-gray-600">Lowest Performer</div>
          <div class="text-xs text-gray-500 mt-1 truncate">{{ bottomPerformerName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import "vue-multiselect/dist/vue-multiselect.css";

canvas {
  max-width: 100%;
}

/* Custom styles for the month input if needed */
input[type="month"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

input[type="month"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
}
</style>
