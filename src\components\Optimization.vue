<script setup lang="ts">
import OptimizationTable from './OptimizationTable.vue';
import FilterBar from './FilterBar.vue';
import FilterBarTest from './FilterBarTest.vue';
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- Fixed Filter Bar at top -->
     <!-- <div class="flex items-center"> -->
       <!-- <div class="flex-shrink-0 bg-primary"> -->
         <FilterBarTest />
       <!-- </div> -->

     <!-- </div> -->
    
    <!-- Scrollable Table Area -->
    <div class="flex-1 overflow-auto p-4 sm:p-6 lg:p-8 ">
      <div class="flex justify-end mb-4">
        <label class="h-9 px-4 bg-tertiary text-white text-sm font-semibold shadow-sm hover:bg-green-900 rounded ml-3 transition-colors flex items-center gap-2 cursor-pointer">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
      <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M12 12V4m0 0l-3 3m3-3l3 3" />
    </svg>
    Exclusion
    
    <input 
      type="file" 
      accept=".csv" 
      class="hidden" 
    />
  </label>
      </div>
      <div class="rounded shadow">
        <div class="p-4">
          <OptimizationTable />
        </div>
      </div>
    </div>
  </div>
</template>