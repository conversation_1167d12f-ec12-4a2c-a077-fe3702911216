<template>
    <div class="">
        <!-- Step Navigation -->
        <div class="p-6 bg-white">
            <div class="flex w-full gap-2 bg-[#F9FAFB] p-2 rounded-lg overflow-x-auto">
                <!-- <div class="px-3 py-1 rounded cursor-pointer flex items-center gap-1" @click="goToHome">
                    <Home class="w-4 h-4" />
                </div> -->
                <template v-for="(step, index) in stepsStore.visibleSteps" :key="index">
                    <div class="px-2 py-1 rounded cursor-pointer flex items-center"
                        :class="{
                            'bg-[#16A34A] text-white': stepsStore.currentStep === index,
                            'bg-[#F3F4F6] text-gray-400 hover:cursor-no-drop': stepsStore.currentStep !== index && step.disabled,
                            
                        }"
                        @click="stepsStore.goToStep(index)">
                        {{ step.name }}
                    </div>
                    <span v-if="index < stepsStore.steps.length - 1" class="text-gray-400 text-sm flex items-center">></span>
                </template>
            </div>
        </div>

        <!-- Step Content -->
         <!-- <div class="w-full overflow-x-auto bg-[#F9FAFB] shadow rounded-lg"> -->
        <div class=" bg-[#F9FAFB] shadow rounded-lg overflow-auto">
            <component  :is="stepsStore.activeStep.component"/>
        </div>

        <!-- Navigation -->
        <div v-if="stepsStore.currentStep !== 0 " class="flex justify-end mt-4">
            <button @click="stepsStore.goToNextStep"
                class="bg-tertiary text-white hover:bg-green-900 px-6 py-2 rounded "
                :disabled="stepsStore.currentStep === stepsStore.steps.length - 1">
                Next →
            </button>
        </div>
    </div>
</template>


<script setup>
import { onMounted, ref, onUnmounted } from 'vue'
import { Home } from 'lucide-vue-next'
import { useRoute, useRouter } from 'vue-router'
import BusinessDashboard from './BusinessDashboard.vue'
import StoreCluster from './StoreCluster.vue'
import ControlStoreSelection from './ControlStoreSelection.vue'
import SpaceDataSummary from './SpaceDataSummary.vue'
import SpaceHealthDashboard from './SpaceHealthDashboard.vue'
import EvaluationDashboard from './EvaluationDashboard.vue'
import OptimizationSummary from './Optimization.vue'
import RangeBasedOptimisationSummary from './OptimizationSummary.vue'
import SaturationPoint from './ProductivityChartsDashboard.vue'
import OutlierHandle from './OutlierHandle.vue'
import PerformanceCalculation from './PerformanceCalculation.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import axios from 'axios'

const stepsStore = useStepsStore()
const currentStep = ref(0)
const router = useRouter()
const route = useRoute()

onUnmounted(() => {
  stepsStore.clearScenarioData()
})
const goToHome = () => {
    router.push({ name: '/spaceoptimization/HomePage' })
}
const scenario_id = null
onMounted(() => {
  const status = route.query.status
  const id = route.query.id
  console.log("status value is ", status)
  if (status === 'create') {
    // createNewOptimizer()
    return
  } else if (status === 'update') {
    console.log("inside status ")
    getOptimizer(id)
  } else {
    router.push({ name: 'HomePage' })
  }

})

const getOptimizer = async (id) => {
  try {
    const response = await axios.post('/scenario/getOptimizerDetails/', {
      "scenario_id": id
    })
    stepsStore.setScenarioData(response.data.data)
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}
</script>

<style scoped>
/* Optional scroll behavior */
::-webkit-scrollbar {
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 4px;
}
</style>