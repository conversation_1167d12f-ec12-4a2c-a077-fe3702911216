<template>
    <div class="group">
      <div
        :class="[
          'relative border-2 border-dashed rounded-2xl p-4 text-center transition-all duration-300 cursor-pointer overflow-hidden',
          isActive ? 'border-secondary bg-primary scale-105' : 'border-primary hover:border-secondary',
          file ? 'bg-primary border-secondary' : 'bg-white hover:bg-gray-50'
        ]"
        @drop.prevent="onDrop"
        @dragover.prevent
        @dragenter.prevent="onDragEnter"
        @dragleave="onDragLeave"
        @click="triggerFileSelect"
      >
        <div class="absolute inset-0 bg-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
  
        <div class="relative z-10">
          <div :class="['w-16 h-16 mx-auto mb-2 rounded-2xl flex items-center justify-center transition-all duration-300', file ? 'bg-secondary/20' : 'bg-gray-100 group-hover:bg-primary']">
            <component :is="file ? 'FileText' : icon" :class="file ? 'w-8 h-8 text-tertiary' : 'w-8 h-8 text-gray-400 group-hover:text-tertiary transition-colors duration-300'" />
          </div>
  
          <div v-if="file" class="space-y-2">
            <p class="font-semibold text-tertiary truncate max-w-full">{{ file.name }}</p>
            <p class="text-sm text-tertiary">Successfully uploaded</p>
            <div class="w-full bg-tertiary rounded-full h-1">
              <div class="bg-tertiary h-1 rounded-full w-full"></div>
            </div>
          </div>
          <div v-else class="space-y-2">
            <p class="font-medium text-gray-700 group-hover:text-emerald-700 transition-colors duration-300">{{ label }}</p>
            <p class="text-sm text-gray-500">Drop file here or click to browse</p>
            <p class="text-xs text-gray-400">Excel or CSV files only</p>
          </div>
        </div>
  
        <input
          :id="`file-${fileKey}`"
          ref="fileInput"
          type="file"
          class="hidden"
          accept=".xlsx,.xls,.csv"
          @change="onFileChange"
        />
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, inject } from 'vue'
  import { FileText } from 'lucide-vue-next'
  
  const props = defineProps({
    fileKey: String,
    label: String,
    icon: Object
  })
  
  const dragActive = inject('dragActive')
  const uploadedFiles = inject('uploadedFiles')
  const setDragActive = inject('setDragActive')
  const handleFileUpload = inject('handleFileUpload')
  
  const fileInput = ref(null)
  const file = computed(() => uploadedFiles.value[props.fileKey])
  const isActive = computed(() => dragActive.value === props.fileKey)
  
  const onFileChange = (event) => {
    handleFileUpload(props.fileKey, event.target.files[0])
  }
  
  const triggerFileSelect = () => {
    fileInput.value?.click()
  }
  
  const onDragEnter = () => {
    setDragActive(props.fileKey)
  }
  
  const onDragLeave = () => {
    setDragActive(null)
  }
  
  const onDrop = (event) => {
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      handleFileUpload(props.fileKey, droppedFile)
    }
    setDragActive(null)
  }
  </script>
  