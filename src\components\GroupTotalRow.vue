<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  group: string;
  totals: any;
  isExpanded: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['toggle']);

// Format numbers for display
const formatNumber = (value: number): string => {
  return value.toLocaleString();
};

// Format decimal with 2 digits
const formatDecimal = (value: number): string => {
  return value.toFixed(2);
};

// Format percentage
const formatPercent = (value: number): string => {
  return value.toFixed(1) + '%';
};

// Compute CSS classes for GMV change
const gmvChangeClass = computed(() => {
  if (props.totals.gmvChange > 0) return 'text-positive';
  if (props.totals.gmvChange < 0) return 'text-negative';
  return '';
});

// Toggle group expansion
const toggle = () => {
  emit('toggle', props.group);
};

// Compute CSS classes for space change
const spaceChangeClass = computed(() => {
  const spaceChange = (props.totals.optimizedLm / props.totals.currentLm - 1) * 100;
  if (spaceChange > 0) return 'text-positive';
  if (spaceChange < 0) return 'text-negative';
  return '';
});

// Compute the space change percentage
const spaceChangePercent = computed(() => {
  return ((props.totals.optimizedLm / props.totals.currentLm - 1) * 100).toFixed(1) + '%';
});
</script>

<template>
  <tr @click="toggle" class="bg-gray-50 font-bold cursor-pointer hover:bg-gray-100 transition-colors">

    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">
      <div class="flex items-center">
      <span class="mr-2">
          <svg v-if="isExpanded" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </span>
      21404
      </div>
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">Dalma Mall</td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap">

        {{ group }} Total
      
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap" colspan="3"></td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatNumber(props.totals.productivity) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right" colspan="2"></td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap"></td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right" colspan="2"></td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(props.totals.currentLm) }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatDecimal(props.totals.optimizedLm) }}
    </td>
    <td class="px-6 py-3 text-sm whitespace-nowrap text-right" :class="spaceChangeClass">
      {{ spaceChangePercent }}
    </td>
    <td class="px-6 py-3 text-sm text-gray-900 whitespace-nowrap text-right">
      {{ formatNumber(props.totals.optimizedGmv) }}
    </td>
    <td class="px-6 py-3 text-sm whitespace-nowrap text-right" :class="gmvChangeClass">
      {{ formatPercent(props.totals.gmvChange) }}
    </td>
  </tr>
</template>