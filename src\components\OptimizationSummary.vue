<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue';
import { useStepsStore } from '../stores/NavigationStore.js'
import FilterBarTest from './FilterBarTest.vue';


const preSeasonEnabled = computed(() => {
    return stepsStore.visibleSteps.find(n => n.name === 'Optimization Summary') || null;
});


const stepsStore = useStepsStore()

// SpaceHealthTables logic
const optimizationSummary = [
  {
    "GRP_NM": "HOMEWARE",
    "DPT_NM": "LIGHTING",
    "CLSS_NM": "TEXTILES",
    "SUB_CLSS_NM": "ACCENTS AND STORAGE",
    "LOC_CD": "80855",
    "MONTH": "202310",
    "TOTAL_LM": 21.2,
    "MIN_LM": 4.994629,
    "current_cover_in_days": 57.64,
    "GMV_PER_LM": 227.53,
    "GMV": 4088.74,
    "NET_SLS_AMT": 11093.38,
    "Performance": 0.709780083,
    "GMV_sum_reference_month": 10679.6,
    "NET_SLS_AMT_sum_reference_month": 18753.37,
    "current_lm": 18,
    "max_sat_lm": 39.55,
    "GMV_per_linear_meter_ref_months": 374.2266691,
    "NET_SLS_AMT_per_linear_meter_ref_months": 537.4266639,
    "cover_penalty": 0.7,
    "adjusted_performance": 0.179134524,
    "perf_bucket": "H",
    "lm_bucket": "H",
    "cover_bucket": "H",
    "action": "keep",
    "status": "Auto-calculated",
    "optimized_lm": 8,
    "lm_delta": -1,
    "change": "Increase",
    "spacechange%": -0.4,
    "space_change_absolute": 1.87,
    "optimized_lm_before_devi_adjust": 12,
    "new_metric": 21042.86,
    "days_btw_reference_period": 90,
    "current_per_day_metric": 418.77,
    "current_per_day_metric_per_lm": 17.78527262,
    "new_metric_per_day": 276.36
  },
  {
    "GRP_NM": "ELECTRONICS",
    "DPT_NM": "APPLIANCES",
    "CLSS_NM": "LAMPS",
    "SUB_CLSS_NM": "TABLEWARE",
    "LOC_CD": "99594",
    "MONTH": "202403",
    "TOTAL_LM": 21.92,
    "MIN_LM": 1.938558,
    "current_cover_in_days": 150.73,
    "GMV_PER_LM": 142.46,
    "GMV": 6685.64,
    "NET_SLS_AMT": 7921.48,
    "Performance": 0.190231552,
    "GMV_sum_reference_month": 12602.53,
    "NET_SLS_AMT_sum_reference_month": 20547.99,
    "current_lm": 12,
    "max_sat_lm": 53.58,
    "GMV_per_linear_meter_ref_months": 301.8150335,
    "NET_SLS_AMT_per_linear_meter_ref_months": 374.7989436,
    "cover_penalty": 0.6,
    "adjusted_performance": 0.273802041,
    "perf_bucket": "L",
    "lm_bucket": "L",
    "cover_bucket": "H",
    "action": "keep",
    "status": "Overridden",
    "optimized_lm": 12,
    "lm_delta": 0,
    "change": "Keep",
    "spacechange%": -9.91,
    "space_change_absolute": -2.44,
    "optimized_lm_before_devi_adjust": 20,
    "new_metric": 17968.23,
    "days_btw_reference_period": 64,
    "current_per_day_metric": 339.09,
    "current_per_day_metric_per_lm": 20.25378501,
    "new_metric_per_day": 160.16
  },
  {
    "GRP_NM": "HOMEWARE",
    "DPT_NM": "LIGHTING",
    "CLSS_NM": "TEXTILES",
    "SUB_CLSS_NM": "ACCENTS AND STORAGE",
    "LOC_CD": "80855",
    "MONTH": "202310",
    "TOTAL_LM": 21.2,
    "MIN_LM": 4.994629,
    "current_cover_in_days": 57.64,
    "GMV_PER_LM": 227.53,
    "GMV": 4088.74,
    "NET_SLS_AMT": 11093.38,
    "Performance": 0.709780083,
    "GMV_sum_reference_month": 10679.6,
    "NET_SLS_AMT_sum_reference_month": 18753.37,
    "current_lm": 18,
    "max_sat_lm": 39.55,
    "GMV_per_linear_meter_ref_months": 374.2266691,
    "NET_SLS_AMT_per_linear_meter_ref_months": 537.4266639,
    "cover_penalty": 0.7,
    "adjusted_performance": 0.179134524,
    "perf_bucket": "H",
    "lm_bucket": "H",
    "cover_bucket": "H",
    "action": "keep",
    "status": "Auto-calculated",
    "optimized_lm": 8,
    "lm_delta": -1,
    "change": "Increase",
    "spacechange%": -0.4,
    "space_change_absolute": 1.87,
    "optimized_lm_before_devi_adjust": 12,
    "new_metric": 21042.86,
    "days_btw_reference_period": 90,
    "current_per_day_metric": 418.77,
    "current_per_day_metric_per_lm": 17.78527262,
    "new_metric_per_day": 276.36
  },
  {
    "GRP_NM": "ELECTRONICS",
    "DPT_NM": "APPLIANCES",
    "CLSS_NM": "LAMPS",
    "SUB_CLSS_NM": "TABLEWARE",
    "LOC_CD": "99594",
    "MONTH": "202403",
    "TOTAL_LM": 21.92,
    "MIN_LM": 1.938558,
    "current_cover_in_days": 150.73,
    "GMV_PER_LM": 142.46,
    "GMV": 6685.64,
    "NET_SLS_AMT": 7921.48,
    "Performance": 0.190231552,
    "GMV_sum_reference_month": 12602.53,
    "NET_SLS_AMT_sum_reference_month": 20547.99,
    "current_lm": 12,
    "max_sat_lm": 53.58,
    "GMV_per_linear_meter_ref_months": 301.8150335,
    "NET_SLS_AMT_per_linear_meter_ref_months": 374.7989436,
    "cover_penalty": 0.6,
    "adjusted_performance": 0.273802041,
    "perf_bucket": "L",
    "lm_bucket": "L",
    "cover_bucket": "H",
    "action": "keep",
    "status": "Overridden",
    "optimized_lm": 12,
    "lm_delta": 0,
    "change": "Keep",
    "spacechange%": -9.91,
    "space_change_absolute": -2.44,
    "optimized_lm_before_devi_adjust": 20,
    "new_metric": 17968.23,
    "days_btw_reference_period": 64,
    "current_per_day_metric": 339.09,
    "current_per_day_metric_per_lm": 20.25378501,
    "new_metric_per_day": 160.16
  },
  {
    "GRP_NM": "FURNITURE",
    "DPT_NM": "SEATING",
    "CLSS_NM": "SOFAS",
    "SUB_CLSS_NM": "RECLINERS",
    "LOC_CD": "74520",
    "MONTH": "202401",
    "TOTAL_LM": 16.0,
    "MIN_LM": 3.250000,
    "current_cover_in_days": 35.21,
    "GMV_PER_LM": 520.40,
    "GMV": 8326.40,
    "NET_SLS_AMT": 12450.79,
    "Performance": 0.885102,
    "GMV_sum_reference_month": 14000.20,
    "NET_SLS_AMT_sum_reference_month": 28900.80,
    "current_lm": 15,
    "max_sat_lm": 25.00,
    "GMV_per_linear_meter_ref_months": 510.11,
    "NET_SLS_AMT_per_linear_meter_ref_months": 565.55,
    "cover_penalty": 0.8,
    "adjusted_performance": 0.120510,
    "perf_bucket": "H",
    "lm_bucket": "M",
    "cover_bucket": "L",
    "action": "reduce",
    "status": "Auto-calculated",
    "optimized_lm": 10,
    "lm_delta": -5,
    "change": "Reduce",
    "spacechange%": -31.25,
    "space_change_absolute": -5.0,
    "optimized_lm_before_devi_adjust": 12,
    "new_metric": 17321.90,
    "days_btw_reference_period": 30,
    "current_per_day_metric": 577.36,
    "current_per_day_metric_per_lm": 38.49,
    "new_metric_per_day": 577.40
  },
  {
    "GRP_NM": "SPORTS",
    "DPT_NM": "FITNESS",
    "CLSS_NM": "EQUIPMENT",
    "SUB_CLSS_NM": "CARDIO",
    "LOC_CD": "23002",
    "MONTH": "202311",
    "TOTAL_LM": 28.5,
    "MIN_LM": 7.890000,
    "current_cover_in_days": 90.54,
    "GMV_PER_LM": 340.29,
    "GMV": 9708.27,
    "NET_SLS_AMT": 14200.55,
    "Performance": 0.632188,
    "GMV_sum_reference_month": 20000.00,
    "NET_SLS_AMT_sum_reference_month": 42000.00,
    "current_lm": 30,
    "max_sat_lm": 35.00,
    "GMV_per_linear_meter_ref_months": 575.50,
    "NET_SLS_AMT_per_linear_meter_ref_months": 789.40,
    "cover_penalty": 0.3,
    "adjusted_performance": 0.189657,
    "perf_bucket": "M",
    "lm_bucket": "M",
    "cover_bucket": "M",
    "action": "keep",
    "status": "Auto-calculated",
    "optimized_lm": 29,
    "lm_delta": -1,
    "change": "Reduce",
    "spacechange%": -3.33,
    "space_change_absolute": -1.0,
    "optimized_lm_before_devi_adjust": 30,
    "new_metric": 21001.20,
    "days_btw_reference_period": 100,
    "current_per_day_metric": 210.01,
    "current_per_day_metric_per_lm": 7.00,
    "new_metric_per_day": 210.00
  },
  {
    "GRP_NM": "HOMEWARE",
    "DPT_NM": "DECOR",
    "CLSS_NM": "CURTAINS",
    "SUB_CLSS_NM": "WINDOW TREATMENTS",
    "LOC_CD": "88001",
    "MONTH": "202308",
    "TOTAL_LM": 11.0,
    "MIN_LM": 2.200000,
    "current_cover_in_days": 25.48,
    "GMV_PER_LM": 300.15,
    "GMV": 3301.65,
    "NET_SLS_AMT": 5201.20,
    "Performance": 0.456710,
    "GMV_sum_reference_month": 5200.15,
    "NET_SLS_AMT_sum_reference_month": 7300.80,
    "current_lm": 10,
    "max_sat_lm": 12.00,
    "GMV_per_linear_meter_ref_months": 280.15,
    "NET_SLS_AMT_per_linear_meter_ref_months": 420.55,
    "cover_penalty": 0.5,
    "adjusted_performance": 0.228355,
    "perf_bucket": "M",
    "lm_bucket": "L",
    "cover_bucket": "M",
    "action": "increase",
    "status": "Auto-calculated",
    "optimized_lm": 12,
    "lm_delta": 2,
    "change": "Increase",
    "spacechange%": 18.18,
    "space_change_absolute": 2.0,
    "optimized_lm_before_devi_adjust": 11,
    "new_metric": 5399.00,
    "days_btw_reference_period": 60,
    "current_per_day_metric": 89.98,
    "current_per_day_metric_per_lm": 8.18,
    "new_metric_per_day": 90.00
  },
  {
    "GRP_NM": "ELECTRONICS",
    "DPT_NM": "MOBILE",
    "CLSS_NM": "SMARTPHONES",
    "SUB_CLSS_NM": "ANDROID",
    "LOC_CD": "55512",
    "MONTH": "202402",
    "TOTAL_LM": 33.0,
    "MIN_LM": 16.000000,
    "current_cover_in_days": 180.1,
    "GMV_PER_LM": 1000.13,
    "GMV": 33004.29,
    "NET_SLS_AMT": 44235.40,
    "Performance": 0.990221,
    "GMV_sum_reference_month": 55000.00,
    "NET_SLS_AMT_sum_reference_month": 66000.00,
    "current_lm": 33,
    "max_sat_lm": 41.00,
    "GMV_per_linear_meter_ref_months": 1200.05,
    "NET_SLS_AMT_per_linear_meter_ref_months": 2000.25,
    "cover_penalty": 0.4,
    "adjusted_performance": 0.3960884,
    "perf_bucket": "H",
    "lm_bucket": "H",
    "cover_bucket": "L",
    "action": "keep",
    "status": "Overridden",
    "optimized_lm": 33,
    "lm_delta": 0,
    "change": "Keep",
    "spacechange%": 0,
    "space_change_absolute": 0,
    "optimized_lm_before_devi_adjust": 32,
    "new_metric": 67000.90,
    "days_btw_reference_period": 28,
    "current_per_day_metric": 2392.89,
    "current_per_day_metric_per_lm": 72.51,
    "new_metric_per_day": 2392.89
  },
  {
    "GRP_NM": "FURNITURE",
    "DPT_NM": "TABLES",
    "CLSS_NM": "DINING",
    "SUB_CLSS_NM": "GLASS",
    "LOC_CD": "61590",
    "MONTH": "202309",
    "TOTAL_LM": 15.55,
    "MIN_LM": 2.950000,
    "current_cover_in_days": 45.52,
    "GMV_PER_LM": 150.00,
    "GMV": 2332.50,
    "NET_SLS_AMT": 3299.99,
    "Performance": 0.309002,
    "GMV_sum_reference_month": 3440.00,
    "NET_SLS_AMT_sum_reference_month": 3550.01,
    "current_lm": 12,
    "max_sat_lm": 18.50,
    "GMV_per_linear_meter_ref_months": 189.09,
    "NET_SLS_AMT_per_linear_meter_ref_months": 287.60,
    "cover_penalty": 0.9,
    "adjusted_performance": 0.0309002,
    "perf_bucket": "L",
    "lm_bucket": "L",
    "cover_bucket": "L",
    "action": "reduce",
    "status": "Auto-calculated",
    "optimized_lm": 8,
    "lm_delta": -4,
    "change": "Reduce",
    "spacechange%": -33.33,
    "space_change_absolute": -4,
    "optimized_lm_before_devi_adjust": 10,
    "new_metric": 3199.90,
    "days_btw_reference_period": 31,
    "current_per_day_metric": 103.22,
    "current_per_day_metric_per_lm": 8.60,
    "new_metric_per_day": 103.22
  },
  {
    "GRP_NM": "HOMEWARE",
    "DPT_NM": "KITCHEN",
    "CLSS_NM": "COOKWARE",
    "SUB_CLSS_NM": "NON-STICK",
    "LOC_CD": "71234",
    "MONTH": "202307",
    "TOTAL_LM": 8.0,
    "MIN_LM": 1.009999,
    "current_cover_in_days": 12.88,
    "GMV_PER_LM": 90.19,
    "GMV": 721.52,
    "NET_SLS_AMT": 1299.88,
    "Performance": 0.092110,
    "GMV_sum_reference_month": 1000.00,
    "NET_SLS_AMT_sum_reference_month": 2100.00,
    "current_lm": 6,
    "max_sat_lm": 9.00,
    "GMV_per_linear_meter_ref_months": 189.09,
    "NET_SLS_AMT_per_linear_meter_ref_months": 350.00,
    "cover_penalty": 0.2,
    "adjusted_performance": 0.018422,
    "perf_bucket": "L",
    "lm_bucket": "S",
    "cover_bucket": "L",
    "action": "increase",
    "status": "Auto-calculated",
    "optimized_lm": 7,
    "lm_delta": 1,
    "change": "Increase",
    "spacechange%": 16.67,
    "space_change_absolute": 1,
    "optimized_lm_before_devi_adjust": 7,
    "new_metric": 2100.45,
    "days_btw_reference_period": 60,
    "current_per_day_metric": 21.67,
    "current_per_day_metric_per_lm": 3.61,
    "new_metric_per_day": 35.00
  },
  {
    "GRP_NM": "ELECTRONICS",
    "DPT_NM": "WEARABLES",
    "CLSS_NM": "SMARTWATCH",
    "SUB_CLSS_NM": "BANDS",
    "LOC_CD": "44991",
    "MONTH": "202405",
    "TOTAL_LM": 9.5,
    "MIN_LM": 0.899999,
    "current_cover_in_days": 22.18,
    "GMV_PER_LM": 510.12,
    "GMV": 4846.14,
    "NET_SLS_AMT": 5901.23,
    "Performance": 0.402321,
    "GMV_sum_reference_month": 9996.32,
    "NET_SLS_AMT_sum_reference_month": 12434.56,
    "current_lm": 7,
    "max_sat_lm": 12.50,
    "GMV_per_linear_meter_ref_months": 714.89,
    "NET_SLS_AMT_per_linear_meter_ref_months": 995.01,
    "cover_penalty": 0.5,
    "adjusted_performance": 0.2011605,
    "perf_bucket": "M",
    "lm_bucket": "S",
    "cover_bucket": "L",
    "action": "reduce",
    "status": "Overridden",
    "optimized_lm": 6,
    "lm_delta": -1,
    "change": "Reduce",
    "spacechange%": -14.29,
    "space_change_absolute": -1,
    "optimized_lm_before_devi_adjust": 6,
    "new_metric": 10987.00,
    "days_btw_reference_period": 40,
    "current_per_day_metric": 274.68,
    "current_per_day_metric_per_lm": 39.24,
    "new_metric_per_day": 274.68
  },
  {
    "GRP_NM": "FURNITURE",
    "DPT_NM": "BEDROOM",
    "CLSS_NM": "BEDS",
    "SUB_CLSS_NM": "KING SIZE",
    "LOC_CD": "31210",
    "MONTH": "202312",
    "TOTAL_LM": 13.25,
    "MIN_LM": 2.330000,
    "current_cover_in_days": 19.78,
    "GMV_PER_LM": 350.00,
    "GMV": 4637.50,
    "NET_SLS_AMT": 6783.99,
    "Performance": 0.309821,
    "GMV_sum_reference_month": 8000.00,
    "NET_SLS_AMT_sum_reference_month": 12345.00,
    "current_lm": 12,
    "max_sat_lm": 17.20,
    "GMV_per_linear_meter_ref_months": 320.00,
    "NET_SLS_AMT_per_linear_meter_ref_months": 570.00,
    "cover_penalty": 0.9,
    "adjusted_performance": 0.0309821,
    "perf_bucket": "L",
    "lm_bucket": "M",
    "cover_bucket": "L",
    "action": "increase",
    "status": "Auto-calculated",
    "optimized_lm": 15,
    "lm_delta": 3,
    "change": "Increase",
    "spacechange%": 20,
    "space_change_absolute": 2.65,
    "optimized_lm_before_devi_adjust": 14,
    "new_metric": 15198.81,
    "days_btw_reference_period": 45,
    "current_per_day_metric": 338.87,
    "current_per_day_metric_per_lm": 28.24,
    "new_metric_per_day": 338.87
  }
]

const applyFilters = () => {
    console.log('Applying filters...');
};

const editedRows = ref(new Set())
const editingRows = ref(new Set()) // Track which rows are in edit mode
const savingRows = ref(new Set())
const isSavingAll = ref(false)
const originalValues = reactive(new Map())

// Helper function to get space change percent value
const getSpaceChangePercent = (item) => {
  return item.space_change_percent ?? item['space_change_%'] ?? 0
}

// Enable editing mode for a row
const enableEditing = (index) => {
  // Store original values when editing starts
  if (!originalValues.has(index)) {
    const item = optimizationSummary[index]
    originalValues.set(index, {
      space_change_percent: getSpaceChangePercent(item),
      space_change_absolute: item.space_change_absolute || 0
    })
    
    // Initialize the reactive properties if they don't exist
    if (!item.hasOwnProperty('space_change_percent')) {
      item.space_change_percent = getSpaceChangePercent(item)
    }
  }
  editingRows.value.add(index)
}

// Cancel editing mode
const cancelEditing = (index) => {
  // Restore original values
  if (originalValues.has(index)) {
    const original = originalValues.get(index)
    optimizationSummary[index].space_change_percent = original.space_change_percent
    optimizationSummary[index].space_change_absolute = original.space_change_absolute
  }
  
  // Remove from all tracking sets
  editingRows.value.delete(index)
  editedRows.value.delete(index)
  originalValues.delete(index)
}

// Mark row as edited when values change
const markAsEdited = (index) => {
  editedRows.value.add(index)
}

// Save individual row
const saveRow = async (index) => {
  savingRows.value.add(index)
  
  try {
    const item = optimizationSummary[index]
    const payload = {
      id: `${item.LOC_CD}_${item.MONTH}_${item.SUB_CLSS_NM}`, // Create unique identifier
      space_change_percent: parseFloat(item.space_change_percent) || 0,
      space_change_absolute: parseFloat(item.space_change_absolute) || 0
    }
    
    // Replace with your actual API call
    // await updateOptimizationData(payload)
    console.log('Saving row:', payload)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Remove from all tracking sets after successful save
    editingRows.value.delete(index)
    editedRows.value.delete(index)
    originalValues.delete(index)
    
    // Show success message (you can implement toast notifications)
    alert('Row saved successfully!')
    
  } catch (error) {
    console.error('Error saving row:', error)
    alert('Error saving changes. Please try again.')
  } finally {
    savingRows.value.delete(index)
  }
}

// Save all changes
const saveAllChanges = async () => {
  isSavingAll.value = true
  
  try {
    const changedItems = Array.from(editedRows.value).map(index => {
      const item = optimizationSummary[index]
      return {
        id: `${item.LOC_CD}_${item.MONTH}_${item.SUB_CLSS_NM}`,
        space_change_percent: parseFloat(item.space_change_percent) || 0,
        space_change_absolute: parseFloat(item.space_change_absolute) || 0
      }
    })
    
    // Replace with your actual bulk API call
    // await bulkUpdateOptimizationData(changedItems)
    console.log('Saving all changes:', changedItems)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Clear all tracking sets after successful save
    editingRows.value.clear()
    editedRows.value.clear()
    originalValues.clear()
    
    alert('All changes saved successfully!')
    
  } catch (error) {
    console.error('Error saving all changes:', error)
    alert('Error saving changes. Please try again.')
  } finally {
    isSavingAll.value = false
  }
}

// Reset all changes
const resetAllChanges = () => {
  Array.from(editingRows.value).forEach(index => {
    cancelEditing(index)
  })
}

// Formatting functions
const formatNumber = (value) => {
  if (value === null || value === undefined) return '-'
  return typeof value === 'number' ? value.toFixed(2) : value
}

const formatPercentage = (value) => {
  if (value === null || value === undefined) return '-'
  return `${(value * 100).toFixed(1)}%`
}

const formatMonth = (month) => {
  if (!month) return '-'
  const year = month.toString().substring(0, 4)
  const monthNum = month.toString().substring(4, 6)
  return `${monthNum}/${year}`
}

// Styling functions
const getPerformanceClass = (performance) => {
  if (performance >= 0.3) return 'bg-green-100 text-green-800'
  if (performance >= 0.1) return 'bg-yellow-100 text-yellow-800'
  return 'bg-red-100 text-red-800'
}

const getStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case 'overridden': return 'bg-blue-100 text-blue-800'
    case 'active': return 'bg-green-100 text-green-800'
    case 'inactive': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getActionClass = (action) => {
  switch (action?.toLowerCase()) {
    case 'keep': return 'bg-green-100 text-green-800'
    case 'increase': return 'bg-blue-100 text-blue-800'
    case 'decrease': return 'bg-orange-100 text-orange-800'
    case 'remove': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// Initialize space change values if not present
optimizationSummary.forEach(item => {
  if (!item.hasOwnProperty('space_change_percent')) {
    item.space_change_percent = item['space_change_%'] || 0
  }
})

const runAvailableRange = () => {
    console.log('Running on available range with filters:', selectedFilters.value)
}

const runOptimizer = () => {
    console.log('Running optimizer with filters:', selectedFilters.value)
}   
</script>

<template>
    <div class="flex w-full">
        <div class="flex-1 flex flex-col overflow-hidden px-8">
            <div class="mb-4">
                <FilterBarTest />
            </div>
            <div class="flex gap-4 w-full justify-end mb-4">
                <div class="flex gap-2 items-center">
                    <button v-if="preSeasonEnabled && preSeasonEnabled.hidden === false"
                        class="flex bg-tertiary hover:bg-green-900 text-white  py-1 px-4 rounded"
                        @click="runAvailableRange">
                        Run on the available range
                    </button>
                    <button class="flex bg-tertiary hover:bg-green-900 text-white py-1 px-4 rounded"
                        @click="runOptimizer">
                        Run Optimizer
                    </button>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">

    <!-- Table Container with horizontal scroll -->
    <div class="overflow-x-auto max-h-[600px] overflow-y-auto">
      <table class="w-full border-collapse">
        <!-- Table Headers -->
        <thead class="sticky top-0 z-20" style="background-color: white;">
          <tr>
            <!-- Sticky Left Columns -->
            <th class="sticky left-0 z-30 px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider " style="background-color: white; min-width: 120px;">
              Group
            </th>
            <th class="sticky left-[120px] z-30 px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider " style="background-color: white; min-width: 120px;">
              Department
            </th>
            <th class="sticky left-[240px] z-30 px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider " style="background-color: white; min-width: 120px;">
              Class
            </th>
            <th class="sticky left-[360px] z-30 px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider" style="background-color: white; min-width: 140px;">
              Sub Class
            </th>
            
            <!-- Scrollable Columns -->
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Location Code
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[80px]">
              Month
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[90px]">
              Total LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[90px]">
              Min LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[120px]">
              Current Cover Days
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              GMV Per LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[80px]">
              GMV
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[120px]">
              Net Sales Amount
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Performance
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              GMV Sum Ref Month
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[160px]">
              Net Sales Ref Month
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Current LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Max Sat LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              GMV Per LM Ref
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[160px]">
              Net Sales Per LM Ref
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[120px]">
              Cover Penalty
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              Adjusted Performance
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Perf Bucket
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              LM Bucket
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Cover Bucket
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[80px]">
              Action
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[80px]">
              Status
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Optimized LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              LM Delta
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[80px]">
              Change
            </th>
            <!-- Editable Columns with special styling -->
            <th class="px-4 py-3 text-left text-xs font-semibold text-white bg-orange-600 uppercase tracking-wider min-w-[130px]">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                </svg>
                Space Change %
              </div>
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-white bg-orange-600 uppercase tracking-wider min-w-[150px]">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                </svg>
                Space Change Absolute
              </div>
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              Optimized LM Before Adj
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              New Metric
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              Days Between Ref Period
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              Current Per Day Metric
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[160px]">
              Current Per Day Metric/LM
            </th>
            <th class="px-4 py-3 text-left text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[140px]">
              New Metric Per Day
            </th>
            <th class="px-4 py-3 text-center text-xs font-semibold text-tertiary uppercase tracking-wider min-w-[100px]">
              Actions
            </th>
          </tr>
        </thead>
        
        <!-- Table Body -->
        <tbody class="bg-white divide-y divide-gray-200">
          <tr 
            v-for="(item, index) in optimizationSummary" 
            :key="index"
            class="hover:bg-blue-50 transition-colors duration-200"
            :class="editingRows.has(index) ? 'bg-blue-50 border-l-4 border-blue-400' : editedRows.has(index) ? 'bg-orange-50 border-l-4 border-orange-400' : ''"
          >
            <!-- Sticky Left Columns with proper background -->
            <td class="sticky left-0 z-10 px-4 py-3 text-sm text-gray-900  font-medium bg-white">
              <div class="truncate max-w-[100px]" :title="item.GRP_NM">
                {{ item.GRP_NM }}
              </div>
            </td>
            <td class="sticky left-[120px] z-10 px-4 py-3 text-sm text-gray-900  bg-white">
              <div class="truncate max-w-[100px]" :title="item.DPT_NM">
                {{ item.DPT_NM }}
              </div>
            </td>
            <td class="sticky left-[240px] z-10 px-4 py-3 text-sm text-gray-900  bg-white">
              <div class="truncate max-w-[100px]" :title="item.CLSS_NM">
                {{ item.CLSS_NM }}
              </div>
            </td>
            <td class="sticky left-[360px] z-10 px-4 py-3 text-sm text-gray-900 font-medium bg-white">
              <div class="truncate max-w-[120px]" :title="item.SUB_CLSS_NM">
                {{ item.SUB_CLSS_NM }}
              </div>
            </td>
            
            <!-- Scrollable Columns - All fields from JSON -->
            <td class="px-4 py-3 text-sm text-gray-900">{{ item.LOC_CD }}</td>
            <td class="px-4 py-3 text-sm text-gray-900">{{ formatMonth(item.MONTH) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.TOTAL_LM) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.MIN_LM) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.current_cover_in_days) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.GMV_PER_LM) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.GMV) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.NET_SLS_AMT) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    :class="getPerformanceClass(item.Performance)">
                {{ formatPercentage(item.Performance) }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.GMV_sum_reference_month) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.NET_SLS_AMT_sum_reference_month) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.current_lm) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.max_sat_lm) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.GMV_per_linear_meter_ref_months) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.NET_SLS_AMT_per_linear_meter_ref_months) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.cover_penalty) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.adjusted_performance) }}</td>
            <td class="px-4 py-3 text-sm text-center">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                {{ item.perf_bucket }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-center">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                {{ item.lm_bucket }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-center">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-indigo-100 text-indigo-800">
                {{ item.cover_bucket }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    :class="getActionClass(item.action)">
                {{ item.action }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                    :class="getStatusClass(item.status)">
                {{ item.status }}
              </span>
            </td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.optimized_lm) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.lm_delta) }}</td>
            <td class="px-4 py-3 text-sm">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                {{ item.change }}
              </span>
            </td>
            
            <!-- Editable Columns -->
            <td class="px-4 py-3 bg-orange-50">
              <div v-if="!editingRows.has(index)" class="flex items-center justify-between">
                <span class="text-sm text-gray-900">{{ formatNumber(getSpaceChangePercent(item)) }}%</span>
              </div>
              <input
                v-else
                v-model.number="item.space_change_percent"
                @input="markAsEdited(index)"
                type="number"
                step="0.01"
                class="w-full px-2 py-1 text-sm border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-orange-100 border-orange-400"
              />
            </td>
            <td class="px-4 py-3 bg-orange-50">
              <div v-if="!editingRows.has(index)" class="flex items-center justify-between">
                <span class="text-sm text-gray-900">{{ formatNumber(item.space_change_absolute) }}</span>
              </div>
              <input
                v-else
                v-model.number="item.space_change_absolute"
                @input="markAsEdited(index)"
                type="number"
                step="0.1"
                class="w-full px-2 py-1 text-sm border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-orange-100 border-orange-400"
              />
            </td>
            
            <!-- Remaining columns from JSON -->
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.optimized_lm_before_devi_adjust) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.new_metric) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.days_btw_reference_period) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.current_per_day_metric) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.current_per_day_metric_per_lm) }}</td>
            <td class="px-4 py-3 text-sm text-gray-900 text-left">{{ formatNumber(item.new_metric_per_day) }}</td>
            
            <!-- Action Buttons -->
            <td class="px-4 py-3 text-center">
              <div class="flex justify-center space-x-2">
                <!-- Edit Button -->
                <button
                  v-if="!editingRows.has(index)"
                  @click="enableEditing(index)"
                  class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                  </svg>
                  Edit
                </button>
                
                <!-- Save Button -->
                <button
                  v-if="editingRows.has(index) && editedRows.has(index)"
                  @click="saveRow(index)"
                  :disabled="savingRows.has(index)"
                  class="inline-flex items-center px-2 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg v-if="!savingRows.has(index)" class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  <svg v-else class="w-3 h-3 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Save
                </button>
                
                <!-- Cancel Button -->
                <button
                  v-if="editingRows.has(index)"
                  @click="cancelEditing(index)"
                  class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-200 border border-gray-300 rounded hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                  </svg>
                  Cancel
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <!-- Bulk Actions Footer -->
    <div v-if="editingRows.size > 0 || editedRows.size > 0" class="bg-orange-100 border-t border-orange-200 px-6 py-4">
      <div class="flex justify-between items-center">
        <div class="text-sm text-orange-800">
          <strong>{{ editingRows.size }}</strong> row(s) in edit mode
          <span v-if="editedRows.size > 0">, <strong>{{ editedRows.size }}</strong> row(s) have unsaved changes</span>
        </div>
        <div class="space-x-3">
          <button
            @click="resetAllChanges"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-orange-700 bg-orange-200 border border-orange-300 rounded hover:bg-orange-300 focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            Reset All
          </button>
          <button
            @click="saveAllChanges"
            :disabled="isSavingAll || editedRows.size === 0"
            class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="!isSavingAll" class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
            </svg>
            <svg v-else class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Save All Changes
          </button>
        </div>
      </div>
    </div>
  </div>
        </div>
    </div>
</template>


<style scoped>
/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Ensure sticky columns have proper shadows */
.sticky {
    box-shadow: 2px 0 4px -2px rgba(0, 0, 0, 0.1);
}

/* Input focus styles */
input[type="number"]:focus {
    transform: scale(1.02);
    transition: transform 0.1s ease-in-out;
}

/* Table hover effects */
tbody tr:hover .sticky {
    background-color: #eff6ff !important;
}

.run-button {
    padding: 10px 16px;
    background-color: #3B82F6;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}
</style>