import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from 'axios';
import { createPinia } from 'pinia'

const app = createApp(App);
const pinia = createPinia()
app.use(pinia)

export const baseURL = import.meta.env.VITE_BASE_URL;

export const baseFastapiURL = import.meta.env.VITE_FASTAPI_URL;

export const redirectUri = import.meta.env.VITE_AD_REDIRECT_URI;

axios.defaults.baseURL = baseURL;

app.provide("baseImageUrl", import.meta.env.VITE_BASE_PATH + "/assets");

app.config.globalProperties.baseImgUrl = import.meta.env.VITE_BASE_PATH + "/assets";

app.use(router);

app.mount("#app");
