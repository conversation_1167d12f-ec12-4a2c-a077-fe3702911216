<template>
    <div v-if="totalPages > 1" class="flex justify-center items-center mt-4 gap-2">
        <button 
            @click="goToPrevious"
            :disabled="currentPage === 1"
            class="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            Previous
        </button>
        
        <!-- Page numbers (optional - shows current range) -->
        <div class="flex items-center gap-1">
            <span v-if="showFirstPage" class="px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 rounded" @click="goToPage(1)">1</span>
            <span v-if="showFirstEllipsis" class="px-2 py-1 text-sm">...</span>
            
            <span 
                v-for="page in visiblePages" 
                :key="page"
                @click="goToPage(page)"
                :class="[
                    'px-2 py-1 text-sm cursor-pointer rounded transition-colors duration-200',
                    page === currentPage ? 'bg-green-100 text-green-800 font-semibold' : 'hover:bg-gray-100'
                ]">
                {{ page }}
            </span>
            
            <span v-if="showLastEllipsis" class="px-2 py-1 text-sm">...</span>
            <span v-if="showLastPage" class="px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 rounded" @click="goToPage(totalPages)">{{ totalPages }}</span>
        </div>
        
        <button 
            @click="goToNext"
            :disabled="currentPage >= totalPages"
            class="px-3 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
            Next 
         </button>
    </div>
    
    
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
    currentPage: {
        type: Number,
        required: true
    },
    totalPages: {
        type: Number,
        required: true
    },
    totalItems: {
        type: Number,
        default: 0
    },
    pageSize: {
        type: Number,
        default: 10
    },
    showPageInfo: {
        type: Boolean,
        default: true
    },
    maxVisiblePages: {
        type: Number,
        default: 5
    }
})

const emit = defineEmits(['page-changed'])

const goToPrevious = () => {
    if (props.currentPage > 1) {
        emit('page-changed', props.currentPage - 1)
    }
}

const goToNext = () => {
    if (props.currentPage < props.totalPages) {
        emit('page-changed', props.currentPage + 1)
    }
}

const goToPage = (page) => {
    if (page !== props.currentPage && page >= 1 && page <= props.totalPages) {
        emit('page-changed', page)
    }
}

// Calculate visible page numbers
const visiblePages = computed(() => {
    const maxVisible = props.maxVisiblePages
    const current = props.currentPage
    const total = props.totalPages
    
    if (total <= maxVisible) {
        return Array.from({ length: total }, (_, i) => i + 1)
    }
    
    const half = Math.floor(maxVisible / 2)
    let start = Math.max(current - half, 1)
    let end = Math.min(start + maxVisible - 1, total)
    
    if (end - start + 1 < maxVisible) {
        start = Math.max(end - maxVisible + 1, 1)
    }
    
    return Array.from({ length: end - start + 1 }, (_, i) => start + i)
})

const showFirstPage = computed(() => {
    return props.totalPages > props.maxVisiblePages && !visiblePages.value.includes(1)
})

const showLastPage = computed(() => {
    return props.totalPages > props.maxVisiblePages && !visiblePages.value.includes(props.totalPages)
})

const showFirstEllipsis = computed(() => {
    return visiblePages.value[0] > 2
})

const showLastEllipsis = computed(() => {
    return visiblePages.value[visiblePages.value.length - 1] < props.totalPages - 1
})

const startItem = computed(() => {
    return (props.currentPage - 1) * props.pageSize + 1
})

const endItem = computed(() => {
    return Math.min(props.currentPage * props.pageSize, props.totalItems)
})
</script>