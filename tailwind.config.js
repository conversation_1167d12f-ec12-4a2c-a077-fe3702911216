// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['"SFProText"', 'ui-sans-serif', 'system-ui'],
      },
      colors: {
        "primary": "#F9FAFB",      // green-100
        "secondary": "#65A963",    // green-600
        "tertiary": "#16A34A",     // green-700
        "sidebar": "#2C3E50",
        "sidebar-hover": "#34495E",
        "header-bg": "#FFFFFF",
        "main-bg": "#F8FAFC",
        "card-bg": "#FFFFFF",
        "positive": "#10B981",
        "negative": "#EF4444",
        "neutral": "#F59E0B",
        "table-header": "#E5F5E4", // green shade for table headers
        "table-row-hover": "#F1F5F9",
        "table-border": "#E2E8F0",
        // Custom for table striping and group total
        "table-row-odd": "#FFFFFF",
        "table-row-even": "#E5F5E4", // light green
        "group-total-bg": "#D1E7D1", // slightly darker green
        "group-total-text": "#4E844D", // same as tertiary
      },
    },
  },
  plugins: [],
}
